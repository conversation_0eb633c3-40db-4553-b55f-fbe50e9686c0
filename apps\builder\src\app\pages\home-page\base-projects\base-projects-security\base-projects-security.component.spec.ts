import { TestBed } from '@angular/core/testing';
import { ReactiveFormsModule, FormBuilder } from '@angular/forms';
import { RouterTestingModule } from '@angular/router/testing';
import { of, throwError } from 'rxjs';
import { BaseProjectsSecurityComponent } from './base-projects-security.component';
import { GetApiService } from '@builder/shared/services/get-api.service';
import { PostApiService } from '@builder/shared/services/post-api.service';
import { DeleteApiService } from '@builder/shared/services/delete-api.service';
import { PutApiService } from '@builder/shared/services/put-api.service';
import { ActivatedRoute } from '@angular/router';
import { EdsNotificationService } from '@gfk/ng-lib';

describe('BaseProjectsSecurityComponent', () => {
  let component: BaseProjectsSecurityComponent;

  const getApiServiceMock = {
    getURMUsers: jest.fn().mockReturnValue(of([])),
    getQCProjectAssignedUsers: jest.fn().mockReturnValue(of({ users: [] })),
    getURMUsersByUserId: jest.fn().mockReturnValue(of([])),
  };

  const postApiServiceMock = {
    addUsersForQCSecurity: jest.fn().mockReturnValue(of({ projectResponses: [] })),
  };

  const deleteApiServiceMock = {
    removeAssignedUsers: jest.fn().mockReturnValue(of({})),
  };

  const putApiServiceMock = {
    // Add mock functions if needed
  };

  const notificationServiceMock = {
    showNotification: jest.fn(),
  };

  const activatedRouteMock = {
    snapshot: {
      paramMap: {
        get: jest.fn().mockReturnValue('123'),
      },
    },
  };

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [ReactiveFormsModule, RouterTestingModule],
      declarations: [BaseProjectsSecurityComponent],
      providers: [
        FormBuilder,
        { provide: GetApiService, useValue: getApiServiceMock },
        { provide: PostApiService, useValue: postApiServiceMock },
        { provide: DeleteApiService, useValue: deleteApiServiceMock },
        { provide: PutApiService, useValue: putApiServiceMock },
        { provide: EdsNotificationService, useValue: notificationServiceMock },
        { provide: ActivatedRoute, useValue: activatedRouteMock },
      ],
    }).compileComponents();

    const fixture = TestBed.createComponent(BaseProjectsSecurityComponent);
    component = fixture.componentInstance;
  });

  it('should create the component', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize addBpSecurityUsersForm correctly', () => {
    component.addBpSecurityUsersFormInitialization();
    expect(component.addBpSecurityUsersForm).toBeTruthy();
    expect(component.addBpSecurityUsersForm.get('users')).toBeTruthy();
  });

  it('should call getURMUsers on getUsersList', () => {
    component.getUsersList();
    expect(getApiServiceMock.getURMUsers).toHaveBeenCalled();
  });

  it('should call addUsersForQCSecurity on addBpSecurityUsers with payload', () => {
    jest.spyOn(component, 'getAssignedBpUserIds').mockReturnValue(of([1, 2, 3]));

    component.addBpSecurityUsers(true);

    expect(postApiServiceMock.addUsersForQCSecurity).toHaveBeenCalledWith({
      projectTypeId: 1,
      UserIds: [1, 2, 3],
      projectIds: [component.baseProjectId],
    });
  });

  it('should call removeAssignedUsers on removeAssignedBpUsers', () => {
    jest.spyOn(component, 'selectedEntities', 'get').mockReturnValue([{ id: 1 }, { id: 2 }]);

    component.removeAssignedBpUsers(true);

    expect(deleteApiServiceMock.removeAssignedUsers).toHaveBeenCalledWith({
      projectTypeId: 1,
      userIds: [1, 2],
      projectId: component.baseProjectId,
    });
  });


  it('should handle pagination changes correctly', () => {
    component.assignedBpUsersData = Array(50).fill({});

    component.onPageChangeAssignedBpUsers({
      totalPages: 5,
      pageIndex: 2,
      pageSize: 10,
      previousPageIndex: 1,
    });

    expect(component.currentPageForAssignedBpUsers).toBe(2);
    expect(component.pageSizeForAssignedBpUsers).toBe(10);
    expect(component.visibleAssignedBpUsersList.length).toBe(10);
  });
});
