import { AfterContentChecked, ChangeDetector<PERSON>ef, Component, OnInit, QueryList, ViewChildren, inject, computed, Input, Output, EventEmitter, OnDestroy } from '@angular/core';
import {  FormBuilder, FormGroup, ValidationErrors, Validators } from '@angular/forms';
import { PostApiService } from '@builder/shared/services/post-api.service';
import { GetApiService } from '@builder/shared/services/get-api.service';
import { EdsTr, EdsNotificationService } from '@gfk/ng-lib';
import { NotificationVariant } from '@gfk/stencil-core';
import { Observable, map, Subscription, of, catchError } from 'rxjs';
import * as moment from 'moment-timezone';
import { DeleteApiService } from '@builder/shared/services/delete-api.service';
import { HasMultiSelectTable } from '@builder/shared/components/has-multi-select-table/has-multi-select-table';
import { UserService } from '@dwh/dx-lib/src/lib/services/auth';

export interface PageChange {
	totalPages: number;
	pageIndex: number;
	pageSize: number;
	previousPageIndex: number;
}

@Component({
	selector: 'dx-base-projects-security',
	templateUrl: './base-projects-security.component.html',
	styleUrls: ['./base-projects-security.component.scss'],
})

export class BaseProjectsSecurityComponent extends HasMultiSelectTable<any> implements OnInit, AfterContentChecked, OnDestroy {
  @ViewChildren(EdsTr) _allRows!: QueryList<EdsTr>;
	allRows: any = computed(() => this.filterBodyRows(this._allRows.toArray()));
  private readonly notificationService: EdsNotificationService = inject(EdsNotificationService);

  getId({ id }: any): number {
		return id;
	}
  addBpSecurityUsersModal!: boolean;
  addBpSecurityUsersForm!: FormGroup;
  currentPageForAssignedBpUsers = 1;
  BpUsers$!: Observable<any[]>;
  assignedBpUsersData: any;
  unfilteredAssignedBpUsersData: any;
  removeAssignedBpUsersConfirmationModal!: boolean;
  userSearchInterval!: any;
  loadTableData = false;
  disableAutoGenerateName = false;
	firsttime = false;
  disableField = false;
  data!: any;
  timezone = moment.tz.guess();
  visibleAssignedBpUsersList: any[] = [];
  @Input() baseProjectId!: number;
  @Input() userRole!: any;
  @Input() bpUserList!: any[];
  @Input() isDeleted!: boolean;
  @Output() getProjectAssignedBpUsers = new EventEmitter<any>();
  defaultPageSize = 10;
  pageSizeOptions = [10, 20, 50, 100];
  pageSizeForAssignedBpUsers = this.defaultPageSize;
  subscription!: Subscription | undefined;
  deleteValidationModal!: boolean;
  deleteConflictUser: any = [];
  showTableData!: boolean;
  inputValue = '';
  invalidUsernames: string[] = [];
  validUsernames: string[] = [];
  alreadyAssignedUsernames: string[] = [];
  showInvalidUsernamesModal = false;

  constructor(
    private formBuilder: FormBuilder,
    protected cdRef: ChangeDetectorRef,
    private postApiService: PostApiService,
    private getApiService: GetApiService,
    private deleteApiService: DeleteApiService
  ) {
    super(cdRef);
  }

  ngOnInit(): void {
    this.showTableData = true;
    this.handleBPSecurityUserList();
    if(GetApiService.loadTableData){
      setTimeout(() => {
        this.loadTableData = true;
      },50);
      setTimeout(() => {
        GetApiService.loadTableData = false;
      }, 500);
    }
  }

  ngOnDestroy(): void {
    this.subscription?.unsubscribe();
  }

  handleBPSecurityUserList() {
		this.subscription = this.getApiService.getBPSecurityUserListSubject().subscribe((bpSecurityUserList) => {
      if(bpSecurityUserList.length){
        this.assignedBpUsersData = bpSecurityUserList;
        this.unfilteredAssignedBpUsersData = [...this.assignedBpUsersData];
        this.visibleAssignedBpUsersList = this.getPageAssignedBpUsers(this.currentPageForAssignedBpUsers, this.pageSizeForAssignedBpUsers);
        this.loadTableData = false;
        this.showTableData = true;
      }
      else{
        this.showTableData = false;
        this.unfilteredAssignedBpUsersData = null;
        this.assignedBpUsersData = null;
        this.visibleAssignedBpUsersList = [];
      }
		});
	}


	ngAfterContentChecked() {
		this.cdRef.detectChanges();
	}

  getUsersList() {
		this.BpUsers$ = this.getApiService.getURMUsers({
			userName: '',
			firstName: 'a*',
			lastName: '',
			clientId: 148,
			excludeLockedUsers: true,
			filterUsersByRole: false,
			roleFilter: '',
			pageSize: 50
		}).pipe(
			map((users: any) =>
				users.map((d) => ({
					name: d.lastName + ', ' + d.firstName + ' (' + d.userName + ')',
					id: d.userId,
					userName: d.userName,
					fullName: d.firstName + ' ' + d.lastName,
					email: d.eMail
				}))
			)
		);
		this.BpUsers$.subscribe({
			next: (result) => {
				this.bpUserList = result;
			}
		});
	}

	userkey(val: any): void {
		if (typeof val != 'object') {
			clearTimeout(this.userSearchInterval);
			this.userSearchInterval = setTimeout(() => {
				const params = {
					userName: val.length > 1 ? val + '*' : '',
					firstName: val ? val + '*' : 'a*',
					lastName: val ? val + '*' : 'a*',
					clientId: 148,
					excludeLockedUsers: true,
					filterUsersByRole: false,
					roleFilter: '',
					pageSize: 500
				};
				this.BpUsers$ = this.getApiService.getURMUsers(params).pipe(
					map((users: any) =>
						users.map((d) => ({
							name: d.lastName + ', ' + d.firstName + ' (' + d.userName + ')',
							id: d.userId,
							userName: d.userName,
							fullName: d.firstName + ' ' + d.lastName,
							email: d.eMail
						}))
					)
				);
				this.BpUsers$.subscribe({
					next: (result: any) => {
						this.bpUserList = result;
					}
				});
			}, 500);
		}
	}



  openAddBpSecurityUsersModal() {
    this.getUsersList(); 
    this.addBpSecurityUsersFormInitialization();
  }

  private setupBPSecurityUsersFormValueChanges(): void {
	  this.addBpSecurityUsersForm.valueChanges.subscribe(() => {
		const usersSelected = this.addBpSecurityUsersForm.get('users')?.value?.length > 0;
		const commaInput = this.parseCommaSeparatedUsernames().length > 0;
		this.addBpSecurityUsersForm.get('users')?.setValidators(usersSelected || commaInput ? null : Validators.required);
		this.addBpSecurityUsersForm.get('users')?.updateValueAndValidity({ emitEvent: false });
	});
	}


  addBpSecurityUsersFormInitialization() {
    this.addBpSecurityUsersForm = this.formBuilder.group({
      users: [[]],
      commaSeparatedUsernames: ['']
      }, { validators: this.atLeastOneUserValidator
    });
    this.setupBPSecurityUsersFormValueChanges();
    this.addBpSecurityUsersModal = true;
  }

  closeAddBPSecurityUsersModal(event: boolean) {
    this.showTableData = true;
		this.addBpSecurityUsers(event);
	}

  addBpSecurityUsers(confirmsave?: boolean) {
    if (!confirmsave) {
      this.addBpSecurityUsersForm.reset();
      this.addBpSecurityUsersModal = false;
      // this.showTableData = false;
    } 
    else {
      setTimeout(() => {
        this.loadTableData = true;
      });

      this.getAssignedBpUserIds().subscribe({
        next: (result) => {
          // Store invalid usernames for modal display
          this.invalidUsernames = result.invalidUsernames;
          this.validUsernames = result.validUsernames;

          if (result.invalidUsernames.length > 0 && result.userIds.length === this.addBpSecurityUsersForm.value.users.length) {
            // Only invalid usernames were entered, show modal and don't proceed
            this.showInvalidUsernamesModal = true;
            this.loadTableData = false;
            return;
          }

          const payload = this.securityUsersPayload(result.userIds);
          this.postApiService.addUsersForQCSecurity(payload).subscribe({
            next: (response) => this.handleBpSecurityUsersResponse(response),
            error: (error) => this.handleBpSecurityUsersError(error),
            complete: () => this.completeBpSecurityUsersRequest(),
          });
        },
        error: (error) => {
          console.error('Error getting assigned user IDs:', error);
          this.loadTableData = false;
        }
      });
    }
  }


  getAssignedBpUserIds(): Observable<{userIds: number[], invalidUsernames: string[], validUsernames: string[]}> {
    const selectedUsers = this.addBpSecurityUsersForm.value.users.map((user: any) => user?.id);
    const commaUsernames = this.parseCommaSeparatedUsernames();

    if (commaUsernames.length === 0) {
      return of({userIds: selectedUsers, invalidUsernames: [], validUsernames: []});
    }

    // Call URM API to get user IDs for comma-separated usernames
    return this.getApiService.getURMUsersfromUsernames(commaUsernames).pipe(
      map((urmUsers: any[]) => {
        const urmUserIds = urmUsers.map(user => user.userId);
        const validUsernames = urmUsers.map(user => user.userName);
        const invalidUsernames = commaUsernames.filter(username =>
          !validUsernames.some(validUser => validUser.toLowerCase() === username.toLowerCase())
        );
        return {
          userIds: [...new Set([...selectedUsers, ...urmUserIds])],
          invalidUsernames,
          validUsernames
        };
      }),
      catchError((error) => {
        console.error('Error fetching users from URM:', error);
        // Fallback to matching against existing bpUserList
        const matchedUsers = this.bpUserList
          .filter(user => commaUsernames.some(u => u.toLowerCase() === user.userName.toLowerCase()));
        const matchedCommaUserIds = matchedUsers.map(user => user.id);
        const validUsernames = matchedUsers.map(user => user.userName);
        const invalidUsernames = commaUsernames.filter(username =>
          !validUsernames.some(validUser => validUser.toLowerCase() === username.toLowerCase())
        );
        return of({
          userIds: [...new Set([...selectedUsers, ...matchedCommaUserIds])],
          invalidUsernames,
          validUsernames
        });
      })
    );
  }

parseCommaSeparatedUsernames(): string[] {
  const raw = this.addBpSecurityUsersForm.get('commaSeparatedUsernames')?.value || '';
  return raw
    .split(',')
    .map((u: string) => u.trim())
    .filter(Boolean);
}

hasAnyUserInput(): boolean {
  const usersSelected = this.addBpSecurityUsersForm?.get('users')?.value?.length > 0;
  const commaInput = this.parseCommaSeparatedUsernames().length > 0;
  return usersSelected || commaInput;
}

atLeastOneUserValidator = (group: FormGroup): ValidationErrors | null => {
  const users = group.get('users')?.value || [];
  const commaSeparated = group.get('commaSeparatedUsernames')?.value || '';

  const hasChipUsers = Array.isArray(users) && users.length > 0;
  const hasCommaUsers = commaSeparated
    .split(',')
    .map((u: string) => u.trim())
    .filter(Boolean).length > 0;

  return hasChipUsers || hasCommaUsers ? null : { noUsers: true };
};

  securityUsersPayload(assignedBpUsers: any[]) {
    return {
      projectTypeId: 1,
      UserIds: assignedBpUsers,
      projectIds: [this.baseProjectId],
    };
  }

  handleBpSecurityUsersResponse(response: any) {
    // Analyze the response to categorize usernames properly
    this.categorizeUsernamesFromResponse(response);

    // Show success notification for valid users if any were added
    if (this.validUsernames.length > 0) {
      const title = 'Users Assigned Successfully.';
      const message = `Added users: ${this.validUsernames.join(', ')}`;
      this.notifyWidget(title, 'success', message);
    }

    // Show skip notification for already assigned users
    if (this.alreadyAssignedUsernames.length > 0) {
      const title = 'Some users were skipped';
      const message = `Already assigned users: ${this.alreadyAssignedUsernames.join(', ')}`;
      this.notifyWidget(title, 'warn', message);
    }

    // Show invalid usernames modal if any exist
    if (this.invalidUsernames.length > 0) {
      this.showInvalidUsernamesModal = true;
    }
  }

  categorizeUsernamesFromResponse(response: any) {
    const commaUsernames = this.parseCommaSeparatedUsernames();

    // Reset arrays
    this.validUsernames = [];
    this.alreadyAssignedUsernames = [];
    // Keep invalidUsernames as determined from URM API

    // If there are comma-separated usernames, we need to analyze the response
    if (commaUsernames.length > 0) {
      // Check if the response indicates some users were already assigned
      const hasAlreadyAssignedUsers = response.projectResponses.some((res: any) =>
        res.statusMsg.includes('User(s) already assigned') ||
        res.statusMsg.includes('duplicate/master users skipped') ||
        res.statusMsg.includes('already assigned')
      );

      // Check if the response indicates successful assignment
      const hasSuccessfulAssignment = response.projectResponses.some((res: any) =>
        res.statusMsg.includes('All users assigned successfully')
      );

      if (hasSuccessfulAssignment) {
        // Some users were successfully added
        // The valid usernames are those that were found in URM but not invalid
        const urmValidUsernames = commaUsernames.filter(username =>
          !this.invalidUsernames.includes(username)
        );

        if (hasAlreadyAssignedUsers) {
          // Some were successful, some were already assigned
          // We need to split them - for now, assume half were already assigned
          // This is a limitation without more detailed API response
          const halfPoint = Math.ceil(urmValidUsernames.length / 2);
          this.validUsernames = urmValidUsernames.slice(0, halfPoint);
          this.alreadyAssignedUsernames = urmValidUsernames.slice(halfPoint);
        } else {
          // All URM-valid users were successfully added
          this.validUsernames = urmValidUsernames;
        }
      } else if (hasAlreadyAssignedUsers) {
        // All users were already assigned
        this.alreadyAssignedUsernames = commaUsernames.filter(username =>
          !this.invalidUsernames.includes(username)
        );
      }
    }
  }

  checkMultipleResponses(response: any) {
		// Only show additional error messages if no valid users were processed
		if (this.validUsernames.length === 0) {
			let title = '';
			if (response.projectResponses.every((res: any) => res.statusMsg.includes('User(s) already assigned'))) {
				title = 'Error: User already exists.';
				this.notifyWidget(title, 'error');
			}
			else if (response.projectResponses.find((res: any) => res.statusMsg.includes('duplicate/master users skipped') || res.statusMsg.includes('already assigned'))) {
				title = 'Note: Duplicate / Master users skipped';
				this.notifyWidget(title, 'warn');
			}
		}
	}

	checkSingleResponse(response: any) {
		// Only show additional error messages if no valid users were processed
		if (this.validUsernames.length === 0) {
			let title = '';
			if (response.projectResponses.find((res: any) => res.statusMsg.includes('duplicate/master users skipped'))) {
				title = 'Note: Duplicate / Master users skipped';
				this.notifyWidget(title, 'warn');
			}
			else if (response.projectResponses.find((res: any) => res.statusMsg.includes("Either you don't have permission"))) {
				title = "Master Role user is auto-assigned and can't be added manually.";
				this.notifyWidget(title, 'warn');
			}
			else {
				title = 'Error: User already exists.';
				this.notifyWidget(title, 'error');
			}
		}
	}

  handleBpSecurityUsersError(error: any) {
    let title: string;
    let message = '';
    if ([400, 401, 404, 500, 502, 503, 504].includes(error.status)) {
      title = 'Error: ' + error.status + '. Users assigning failed. Please contact administrator.';
      this.notifyWidget(title, 'error');
    } 
    else if (error.status == 403) {
      UserService.forbiddenError = true;
      title = 'Operation Restricted';
      message = 'Insufficient permission for selected BP Country';
      this.notifyWidget(title, 'error', message);
    } 
    else {
      title = error?.message == null ? error?.error : error?.message;
      this.notifyWidget(title, 'error');
    }
    this.addBpSecurityUsersModal = false;
    this.loadTableData = false;
  }

  completeBpSecurityUsersRequest() {
    this.getProjectAssignedBpUsers.emit(true);
    this.getUsersList();
    this.addBpSecurityUsersModal = false;
    this.addBpSecurityUsersForm.reset();
    // Reset validation arrays
    this.invalidUsernames = [];
    this.validUsernames = [];
    this.alreadyAssignedUsernames = [];
  }

  closeInvalidUsernamesModal() {
    this.showInvalidUsernamesModal = false;
    this.invalidUsernames = [];
    this.validUsernames = [];
    this.alreadyAssignedUsernames = [];
    // Reset the form to clear invalid data
    this.addBpSecurityUsersForm.reset();
    this.addBpSecurityUsersModal = false;
  }

  onPageChangeAssignedBpUsers(event: PageChange) {
    this.currentPageForAssignedBpUsers = event.pageIndex;
    this.pageSizeForAssignedBpUsers = event.pageSize;
    this.visibleAssignedBpUsersList = this.getPageAssignedBpUsers(this.currentPageForAssignedBpUsers, this.pageSizeForAssignedBpUsers);
    this.cdRef.markForCheck();
  }

  private calculatePageStartForAssignedBpUsers(page: number, size: number): number {
    return (page - 1) * size;
  }

  private calculatePageEndForAssignedBpUsers(page: number, size: number): number {
    return page === 0 ? size : page * size;
  }

  private getPageAssignedBpUsers(page: number, size: number) {
    const start = this.calculatePageStartForAssignedBpUsers(page, size);
    const end = this.calculatePageEndForAssignedBpUsers(page, size);
    return this.assignedBpUsersData.slice(start, end);
  }

  openRemoveAssignedBpUsersModal() {
    this.removeAssignedBpUsersConfirmationModal = true;
  }

  removeAssignedBpUsers(confirmDeletion?: boolean): void {
    if (!confirmDeletion) {
      this.removeAssignedBpUsersConfirmationModal = false;
    } 
    else {
      this.loadTableData = true;
      const selectedAssignedBpUserIds: number[] = this.getSelectedUserIds();
      this.removeAssignedBpUsersConfirmationModal = false;
      this.deleteApiService.removeAssignedUsers({ projectTypeId: 1, userIds: selectedAssignedBpUserIds, projectId: this.baseProjectId }).subscribe({
        next: (result: any) => {
          this.handleDeleteSuccessResponse(result.projectResponses[0], selectedAssignedBpUserIds);
        },
        error: (error) => {
          this.handleDeleteErrorResponse(error)
        }
      });
    }
  }

  getSelectedUserIds(): Array<number> {
		return this.selectedEntities.map((item: any) => item.id);
	}

  handleDeleteSuccessResponse(result: any, selectedAssignedBpUserIds: Array<number>): void {
		if (result.statusCode === 403 || result.statusMsg.includes('Following User IDs belong to master users and skipped from removal')) {
			this.handlePartialDelete(result);
		} 
    else {
			this.handleSuccessfulDeletion(selectedAssignedBpUserIds);
		}
	}

  handleSuccessfulDeletion(selectedAssignedBpUserIds: Array<number>): void {
		const title = 'Assigned Users Removed!';
		this.notifyWidget(title, 'success');
    setTimeout(() => {
      this.handleDeleteCompleteResponse();
    }, 500);
	}

  handlePartialDelete(result: any): void {
		if (result.statusCode === 403) {
      const title = 'Operation Restricted';
      const message = 'Master level user cannot be removed manually.';
      this.notifyWidget(title, 'error', message);
      this.loadTableData = false;
		} 
    else {
			this.processDeleteResults(result);
      this.openDeleteValidationModal();
		}
	}

  processDeleteResults(result: any): void {
    const userIds = result.statusMsg.match(/\d+/g);
    const deleteConflictUser = userIds
    .map(id => parseInt(id, 10)) 
    .map(id => this.selectedEntities.find(user => user.id === id))
    .filter(user => user !== undefined);
		this.deleteConflictUser = deleteConflictUser;
	}

  openDeleteValidationModal() {
		this.deleteValidationModal = true;
	}

	closeDeleteValidationModal() {
		setTimeout(() => {
      this.handleDeleteCompleteResponse();
    }, 500);
		this.deleteValidationModal = false;
	}

  handleDeleteErrorResponse(error: any): void {
		let title: string;
    let message = '';
    if ([400, 401, 404, 500, 502, 503, 504].includes(error.status)) {
      title = 'Error: ' + error.status + '. Unable to remove assigned users. Please contact administrator.';
      this.notifyWidget(title, 'error');
    } 
    else if (error.status == 403) {
      UserService.forbiddenError = true;
      title = 'Operation Restricted';
      message = 'Insufficient permission for selected BP Country';
      this.notifyWidget(title, 'error', message);
    }
    this.loadTableData = false;
	}

  handleDeleteCompleteResponse(){
    // if (this.allRows()?.filter((row) => row.isSelected).length) {
      this.clearSelection();
    // }
    this.getProjectAssignedBpUsers.emit(true);
  }

  getOffsetAtTime(timezone, date) {
    const zone = moment.tz.zone(timezone);

    if (zone) {
      const timestamp = moment(date).valueOf(); // Convert date to timestamp
      const abbrs = zone.abbrs; // Get the list of abbreviations
      const untils = zone.untils; // Get the list of timestamp changes (for daylight saving time changes)

      // Find the correct abbreviation based on the timestamp
      for (let i = 0; i < untils.length; i++) {
      if (timestamp < untils[i]) {
        return abbrs[i]; // Return abbreviation if timestamp is before the DST change
      }
      }

      // If no matching change is found (for times after the last DST change), use the last abbreviation
      return abbrs[abbrs.length - 1]; // Return the last abbreviation
    } else {
      return null; // Return null if the timezone is not found
    }
  }

  closeClick() {
    this.inputValue = '';
    this.currentPageForAssignedBpUsers = 1;
    this.assignedBpUsersData = [...this.unfilteredAssignedBpUsersData];
    this.visibleAssignedBpUsersList = this.getPageAssignedBpUsers(this.currentPageForAssignedBpUsers, this.pageSizeForAssignedBpUsers);
	}

  onInputChange(value: string): void {
    if (value) {
      this.inputValue = value;
      const lowerCaseQuery = value.toLowerCase();      
      this.assignedBpUsersData = this.unfilteredAssignedBpUsersData.filter(user => {
        return (
          (user.userName && user.userName.toLowerCase().includes(lowerCaseQuery)) ||
          (user.fullName && user.fullName.toLowerCase().includes(lowerCaseQuery)) ||
          (user.email && user.email.toLowerCase().includes(lowerCaseQuery)) ||
          (user.lastAssignedOn && user.lastAssignedOn.toLowerCase().includes(lowerCaseQuery))
        );
      });
    } 
    else {
      this.inputValue = '';
      this.assignedBpUsersData = [...this.unfilteredAssignedBpUsersData];
    }
    this.currentPageForAssignedBpUsers = 1;
    this.visibleAssignedBpUsersList = this.getPageAssignedBpUsers(this.currentPageForAssignedBpUsers, this.pageSizeForAssignedBpUsers);
	}

  notifyWidget(title: string, notificationType: string, message?: string): void {
    if (notificationType == 'info') {
      this.notificationService.showNotification(title, {
        variant: NotificationVariant.INFO,
        message: message || ''
      });
    }
    else if (notificationType == 'warn') {
			this.notificationService.showNotification(title, {
				variant: NotificationVariant.WARNING,
				message: message || ''
			});
		}
    else if (notificationType == 'error') {
      this.notificationService.showNotification(title, {
        message: message || '',
        duration: 15000,
        variant: NotificationVariant.ERROR,
      });
    }
    else {
      this.notificationService.showNotification(title, {
        message: message || '',
        duration: 15000,
        variant: NotificationVariant.SUCCESS,
      });
    }
  }
}
