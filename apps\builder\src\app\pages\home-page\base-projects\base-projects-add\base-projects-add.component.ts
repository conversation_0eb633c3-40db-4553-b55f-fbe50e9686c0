import { After<PERSON>ontent<PERSON>he<PERSON>, ChangeDete<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, inject, HostListener, computed } from '@angular/core';
import { FormArray, FormBuilder, FormGroup, ValidationErrors, Validators } from '@angular/forms';
import { ActivatedRoute, Router, NavigationStart } from '@angular/router';
import { PostApiService } from '@builder/shared/services/post-api.service';
import { GetApiService } from '@builder/shared/services/get-api.service';
import { EdsTr, EdsNotificationService } from '@gfk/ng-lib';
import { NotificationVariant } from '@gfk/stencil-core';
import { RadioLabelsModel } from '@gfk/ng-lib/lib/components/radio-buttons/models/radio-buttons.interface';
import { Observable, Subscription, catchError, firstValueFrom, map, of } from 'rxjs';
import * as moment from 'moment-timezone';
import { DeleteApiService } from '@builder/shared/services/delete-api.service';
import { PutApiService } from '@builder/shared/services/put-api.service';
//QC Period
import { HasMultiSelectTable } from '@builder/shared/components/has-multi-select-table/has-multi-select-table';
import { UserService } from '@dwh/dx-lib/src/lib/services/auth';
import { CanComponentDeactivate } from '@builder/core/guard/can-deactivate.guard';
import { Location } from '@angular/common';
//QC Period

export interface AutocompleteOption {
	value: string;
	label: string;
}

//QC Period
export interface PageChange {
	totalPages: number;
	pageIndex: number;
	pageSize: number;
	previousPageIndex: number;
}
//QC Period

@Component({
	selector: 'dx-base-projects-add',
	templateUrl: './base-projects-add.component.html',
	styleUrls: ['./base-projects-add.component.scss'],
})

export class BaseProjectsAddComponent extends HasMultiSelectTable<any> implements OnInit, OnDestroy, AfterContentChecked, CanComponentDeactivate {
	@ViewChildren(EdsTr) _allRows!: QueryList<EdsTr>;
	allRows: any = computed(() => this.filterBodyRows(this._allRows.toArray()));
	private pendingRoute: string | null = null;
	private readonly notificationService: EdsNotificationService = inject(EdsNotificationService);
	private readonly userService = inject(UserService);

	@ViewChildren(EdsTr) selectedRows!: QueryList<EdsTr>;

	baseProjectPanelLabelGroup: RadioLabelsModel = {
		label: 'Panel *',
	};

	baseProjectPanelOptions: { title: string; inputClass: string; name: string; value: string; hasError: boolean; checked: boolean; disabled: boolean; }[] = [];

	baseProjectTypeLabelGroup: RadioLabelsModel = {
		label: 'Base Project Type *',
	};

	baseProjectTypeOptions: { title: string; inputClass: string; name: string; value: string; hasError: boolean; checked: boolean; disabled: boolean; }[] = [];

	baseProjectNameLabelGroup: RadioLabelsModel = {
		label: 'Base Project Name*',
	};

	baseProjectNameOptions = [
		{ title: "Manual", inputClass: '', name: 'radioGroup', value: '1', hasError: false, checked: false, disabled: false },
		{ title: "Auto-generated", inputClass: '', name: 'radioGroup', value: '2', hasError: false, checked: false, disabled: false }
	];

	dataType$!: Observable<AutocompleteOption[]>;

	purpose$!: Observable<AutocompleteOption[]>;

	countries$!: Observable<AutocompleteOption[]>;

	productGroup$!: Observable<AutocompleteOption[]>;

	periodicities$!: Observable<AutocompleteOption[]>;

	projectSubType$!: Observable<AutocompleteOption[]>;

	baseProject$!: Observable<AutocompleteOption[]>;

	panels$!: Observable<AutocompleteOption[]>;

	subscription!: Subscription | undefined;

	preservedFormState: any = null;
	selectedtabs = 'createBaseProject';
  	isIRSeparationRequested = false;
	isDeleted = false;
	showBCRConflictsTab = false;
	productGroupData: any = [];
	dataTypeData: any = [];
	filteredDataTypeData: any = [];
	purposeData: any = [];
	countriesList: any = [];
	projectSubTypeList: any = [];
	panelList: any = [];
	periodicityData: any = [];
	periodicityDataForFilter: any = [];
	baseProjectData: any = [];
	disableField = false;
	hideFieldsForDistributor = false;
	showError = false;
	baseProjectID!: number;
	typeId!: number;
	isTypeIdValid = false;
	addBaseProjectFormGroup!: FormGroup;
	refreshedDateTime!: string;
	updatedBy!: string;
	data!: any;
	lastUpdateOn!: string;
	selectedProductGroupLabels: Array<string> = [];
	selectedPredecessorBaseProjectLabels: Array<string> = [];
	deleteConfirmationModal!: boolean;
	restoreConfirmationModal!: boolean;
	icon = 'warning';
	editBaseProject: any;
	selectedTab!: string;
	hideQCTabs = false;
	bcrDetailList: any;
	visibleBCRDetailList: any[] = [];
	pageSizeForBCRDetail: any;
	currentPageForBCRDetail = 1;
	bcrDetailsModal!: boolean;
	readonly defaultPageSizeforBCRDetail = 10;
	readonly pageSizeOptionsForBCRDetail: number[] = [10, 25, 50, 100];
	bcrConflictsCount!: any;
	deletedBps: any = [];
	validationMessage: any;
	displayNonDeletedMessage = false;
	displayDeleteWithErrorMessage = false;
	bpDeleteConflictDetails: any;
	deleteValidationModal!: boolean;

	//QC Project Settings
	qcProjectSettingsFormGroup!: FormGroup;
	resetDataCorrectionType$!: Observable<AutocompleteOption[]>;
	resetDataCorrectionTypeList: any = [];
	qcProjectID!: number;
	showSQCFields = false;
	showAutoLoadFields = false;
	createdOn!: string;
	createdBy!: any;
	qcPeriodLastUpdateOn!: string;
	qcPeriodUpdatedBy: any;
	qcPeriodCreatedOn!: string;
	qcPeriodCreatedBy: any;
	sqcModeLabelGroup: RadioLabelsModel = {
		label: 'SQC Mode',
	};
	sqcModeOptions = [
		{ title: "Inactive", inputClass: '', name: 'radioGroup', value: '0', hasError: false, checked: true, disabled: false },
		{ title: "Supervised", inputClass: '', name: 'radioGroup', value: '2', hasError: false, checked: false, disabled: false },
		{ title: "Active", inputClass: '', name: 'radioGroup', value: '1', hasError: false, checked: false, disabled: false }
	];
	disableBPEdit = false;
	disableQCProjectEdit = false;
	disableQCProjectEditField = false;
	hideForm = false;
	showPeriodicityError = false;
	showDistributorPanelError = false;
	bpValidationModal = false;

	bpValidationMessage!: string;
	showDataTypeError = false;
	refPeriodLists: { [index: number]: AutocompleteOption[] } = {};
	deleteConflictUser: any;
	deleteUserValidationModal!: boolean;
	cancelFormChangesModal!: boolean;
	isAutomatedPriceCheck = false;
	isAutoLoad = false;
	//QC Project Settings

	//QC Period
	getId({ id }: any): number {
		return id;
	}
	UnsavedChangesModal!: boolean;
	qcPeriodData!: any;
	visibleQCPeriods: any[] = [];
	deleteQCPeriodConfirmationModal!: boolean;
	qcPeriodFormModal!: boolean;
	qcPeriodFormGroup!: FormGroup;
	bulkQCPeriodFormGroup!: FormGroup;
	periods$!: Observable<AutocompleteOption[]>;
	periodList: any = [];
	currentPeriods$!: Observable<AutocompleteOption[]>;
	currentPeriodList: any = [];
	baseProjectForQCPeriod$!: Observable<AutocompleteOption[]>;
	baseProjectListForQCPeriod: any = [];
	qcPeriodID!: any;
	pageSize: any;
	currentPage = 1;
	readonly defaultPageSize = 10;
	readonly pageSizeOptions: number[] = [10, 25, 50, 100];
	createBulkQCPeriodModal!: boolean;
	periodListForBulkQCPeriod!: any;
	tempFormData: any = null;
	qcperiodError = false;
	showQCPeriodTable = true;
	loadQCPeriodTableData!: boolean;
	projectDetailsCache: Record<number, any> = {};
	projectPeriodicityMap: Record<number, number> = {};
	periodsByPeriodicity: Record<number, any[]> = {};



	//QC Period

	unsavedChanges!: boolean;
	copyBaseProject = false;
	productGroupCount = 0;
	userRole: any;
	disableQCPeriodEdit = false;
	disableAutoGenerateName = false;
	firsttime = false;
	periodswithdate$!: Observable<AutocompleteOption[]>;

	//QC Security
	addQCSecurityUsersModal!: boolean;
	addQCSecurityUsersForm!: FormGroup;
	users$!: Observable<any[]>;
	userList!: any[];
	assignedUsersData: any;
	unfilteredAssignedUsersData: any;
	visibleAssignedUsersList: any[] = [];
	pageSizeForAssignedUsers: any;
	currentPageForAssignedUsers = 1;
	removeAssignedUsersConfirmationModal!: boolean;
	userSearchInterval!: any;
	showQCSecurityTable = true;
	loadQCSecurityTableData!: boolean;
	inputValue = '';
	invalidUsernames: string[] = [];
	showInvalidUsernamesModal = false;
	//QC Security
  	projectTypeId: any;


	QCDeleteValidationModal = false;
	QCPeriodQCStatusIds: any = [];
	QCEditValidationModal =false;
	loadTableData!: boolean;
	timezone = moment.tz.guess();
	pageLoad = true; 
	fetchBPData!: boolean;

	pgEditValidationModal =false;
	pgEditErrorMessage:any;
	showBCRConflictsTabEdit=false;
	initialbaseProjectProductGroup : any= [];
	pgEditQCPeriodValidationModal =false;
	associationsOptions = [
		{ label: 'Production Project', value: 'ProductionProject' },
		{ label: 'Reporting Project', value: 'ReportingProject' },
		{ label: 'RB Base Project', value: 'RBBaseProject' }
	];
	rbbpProjects: any[] = [];
	productionProjects: any[] = [];
	reportingProjects: any[] = [];
	currentProjects: any[] = [];
	visibleCurrentProjects: any[] = [];
	currentProjectType= 'ProductionProject';
	currentPageForAssociatedProjects = 1;
	pageSizeForAssociatedProjects: any;
	showAssociationsTable = true;
	  
	  
	  

	constructor(
		private formBuilder: FormBuilder,
		private getApiService: GetApiService,
		private postApiService: PostApiService,
		private router: Router,
		private activatedRoute: ActivatedRoute,
		private deleteApiService: DeleteApiService,
		private putApiService: PutApiService,
		protected cdRef: ChangeDetectorRef,
		private location: Location
	) {
		super(cdRef);
		this.pageSize = this.defaultPageSize;
		this.pageSizeForAssignedUsers = this.defaultPageSize;
		this.pageSizeForBCRDetail = this.defaultPageSizeforBCRDetail;

		this.location.subscribe((event) => {
			if (this.unsavedChanges) {
				this.pendingRoute = event.url ? event.url : null;
				this.openUnsavedChangesModal();
				this.location.go(this.router.url);
			}
		});

		this.subscription = this.router.events.subscribe((event) => {
			if (event instanceof NavigationStart && this.unsavedChanges) {
				this.pendingRoute = event.url;
				this.openUnsavedChangesModal();
				this.location.go(this.router.url);
			}
		});
		GetApiService.loadTableData = true;
		GetApiService.loadQCPeriodTableData = true;
		GetApiService.loadQCSecurityTableData = true;
	}

	async ngOnInit(): Promise<void> {
		this.initializeUserRole();

		const status = localStorage.getItem(`irRequested_${this.baseProjectID}`);
		this.isIRSeparationRequested = status === 'true';


		this.initializeFormAndLoadDropdownFieldsData();
		this.initializeValueChanges();
		this.afterChildComponentsLoaded();

		// QC Project Settings
		this.getResetCorrectTypeList();

		this.baseProjectID = this.activatedRoute.snapshot.params.id;

		if (this.baseProjectID) {
			await this.preloadAllPeriods();
			setTimeout(() => {
				this.loadBaseProjectDataByID();
			},300);
			this.getUsersList();
			this.getProjectAssignedBpUsers();
			this.getProjectAssociations();
			GetApiService.loadTableData = true;
			GetApiService.loadQCPeriodTableData = true;
			GetApiService.loadQCSecurityTableData = true;
		}
	}


	async preloadAllPeriods() {
		try {
		  const periodicities = [
			2, 5, 4, 6, 17
		  ];
	  
		  const preloadPromises = periodicities.map(async (periodicityId) => {
			try {
			  const periods = await firstValueFrom(
				this.getApiService.getAsyncPeriodsByPeriodicity(periodicityId, 1000).pipe(
				  map((periods: any) =>
					periods.map((p: any) => ({
					  label: `(${p.id}) - ${p.name}`,
					  value: p.id.toString()
					}))
				  )
				)
			  );
			  this.periodsByPeriodicity[periodicityId] = periods;
			} catch (err) {
			  console.error(`Failed to load periods for periodicityId ${periodicityId}`, err);
			  this.periodsByPeriodicity[periodicityId] = [];
			}
		  });
	  
		  await Promise.all(preloadPromises);
		} catch (err) {
		  console.error('Error preloading all periods:', err);
		}
	  }

	initializeUserRole() {
		this.subscription = this.getApiService.getUserRoleSubject().subscribe((userRole) => {
			this.userRole = userRole;
		});
	}

	loadBaseProjectDataByID() {
		this.disableAutoGenerateName = false;
		this.firsttime = true;
		this.getBaseProjectByBaseProjectId(this.baseProjectID);
  	}



	initializeFormAndLoadDropdownFieldsData() {
		this.addBaseProjectFormInitializer();
		this.qcProjectSettingsFormInitializer();
		this.getDataTypeList();
		this.getPurposeList();
		this.getCountryList();
		this.getPeriodicityList();
		this.getProjectSubTypeList();
		this.getPanelList();
	}

	initializeValueChanges() {
		this.addBaseProjectFormGroup.get('suffixes')?.valueChanges.subscribe(() => {
			this.updateCombinedName();
		});
		this.setupBaseProjectCountryChanges();
		this.setupBaseProjectPeriodicityChanges();
		this.setupBaseProjectDataTypeChanges();
	}

	setupBaseProjectCountryChanges() {
		this.subscription = this.addBaseProjectFormGroup.get('baseProjectCountry')?.valueChanges.subscribe((value) => {
			if (value && !this.baseProjectID) {
				this.getValueByBaseProjectCountry(value, this.addBaseProjectFormGroup.get('baseProjectPanel')?.value);
			}
			else {
				if(!this.baseProjectID){
					this.resetProductGroupData();
				}
			}
		});
	}

	resetProductGroupData() {
		this.productGroupData = [];
		this.baseProjectData = [];
		this.addBaseProjectFormGroup.controls.baseProjectProductGroup.setValue([]);
		this.addBaseProjectFormGroup.controls.baseProjectPredecessor.setValue([]);
	}

	setupBaseProjectPeriodicityChanges() {
		this.subscription = this.addBaseProjectFormGroup.get('baseProjectPeriodicity')?.valueChanges.subscribe((value) => {
			if (value) {
				this.showPeriodicityError = false;
				if (value == 2 && this.addBaseProjectFormGroup.get('baseProjectDataType')?.value == 11) {
					this.showPeriodicityError = true;
				}
			}
			else {
				this.showPeriodicityError = false;
			}
		});
	}

	setupBaseProjectDataTypeChanges() {
		this.subscription = this.addBaseProjectFormGroup.get('baseProjectDataType')?.valueChanges.subscribe((value) => {
			if(value && !this.fetchBPData){
				this.showDistributorPanelError = false;
				this.showDataTypeError = false;
				if(!this.pageLoad || !this.baseProjectID || this.copyBaseProject){
					this.filterPeriodicityList(value, true);
					this.handleDataTypeChangeForNewProject(value);
				}
				if(this.editBaseProject){
					this.handleDataTypeChangeForExistingProject(value);
				}
			}
		});
	}

	filterPeriodicityList(value: any, dataTypeValueChange?: boolean){
		if (value == 11) {
			this.periodicityData = this.sortPeriodicities(this.periodicityDataForFilter.filter(item => item.value !== "2" && item.value !== "1"));
		}
		else {
			this.periodicityData = this.sortPeriodicities([...this.periodicityDataForFilter]);
		}
		if(dataTypeValueChange && this.addBaseProjectFormGroup.get('baseProjectPeriodicity')?.value){
			const selectedPeriodicity = this.periodicityData.find(item => item.value == this.addBaseProjectFormGroup.get('baseProjectPeriodicity')?.value);
			if(!selectedPeriodicity){
				this.addBaseProjectFormGroup.get('baseProjectPeriodicity')?.setValue(null);
			}
		}
	}

	handleDataTypeChangeForExistingProject(value: any) {
		if(value){
			const periodicityValue = this.addBaseProjectFormGroup.get('baseProjectPeriodicity')?.value;
			if (periodicityValue == 2) {
				this.showDataTypeError = value == 3 || value == 11;
			}
			else if (periodicityValue == 4) {
				this.showDataTypeError = [1, 2, 5, 6, 10].includes(parseInt(value));
			}
			else {
				this.showDataTypeError = ![4, 7, 9, 11].includes(parseInt(value));
			}
		}
	}


	handleDataTypeChangeForNewProject(value: any) {
		if (value) {
			this.showDistributorPanelError = false;
			const baseProjectPanelValue = this.addBaseProjectFormGroup.get('baseProjectPanel')?.value;
			if ([1, 2, 5, 6, 10].includes(parseInt(value))) {
				this.handleBaseProjectDataType1(value, baseProjectPanelValue);
			}
			else if (value == 3) {
				this.handleBaseProjectDataType2(baseProjectPanelValue);
			}
			else if ([4, 7, 9].includes(parseInt(value))) {
				this.handleBaseProjectDataType3(value, baseProjectPanelValue);
			}
			else if ([8, 11].includes(parseInt(value))) {
				this.resetDistributorPanelError(value, baseProjectPanelValue);
			}
		}
		else {
			this.resetDataTypeState();
		}
	}

	handleBaseProjectDataType1(value: any, panelValue: any) {
		if (panelValue == 3) {
			if (value == 5 || value == 6) {
				this.addBaseProjectFormGroup.get('baseProjectPurpose')?.setValue('1');
				if(!this.baseProjectID || this.copyBaseProject){
					this.addBaseProjectFormGroup.get('baseProjectPeriodicity')?.setValue('2');
					this.addBaseProjectFormGroup.get('baseProjectPeriodicity')?.disable();	
				}
			}
			else {
				this.showDistributorPanelError = true;
			}
		}
		else {
			this.addBaseProjectFormGroup.get('baseProjectPurpose')?.setValue('1');
			if(!this.baseProjectID || this.copyBaseProject){
				this.addBaseProjectFormGroup.get('baseProjectPeriodicity')?.setValue('2');
				this.addBaseProjectFormGroup.get('baseProjectPeriodicity')?.disable();
			}
		}
	}

	handleBaseProjectDataType2(panelValue: any) {
		if (panelValue == 3) {
			this.showDistributorPanelError = true;
		}
		else {
			this.addBaseProjectFormGroup.get('baseProjectPurpose')?.setValue('1');
			if(!this.baseProjectID || this.copyBaseProject){
				this.addBaseProjectFormGroup.get('baseProjectPeriodicity')?.setValue('4');
				this.addBaseProjectFormGroup.get('baseProjectPeriodicity')?.disable();
			}
		}
	}

	handleBaseProjectDataType3(value: any, panelValue: any) {
		if (panelValue == 3) {
			if (value == 7) {
				this.addBaseProjectFormGroup.get('baseProjectPurpose')?.setValue('1');
				if(!this.baseProjectID || this.copyBaseProject){
					this.addBaseProjectFormGroup.get('baseProjectPeriodicity')?.setValue('2');
					this.addBaseProjectFormGroup.get('baseProjectPeriodicity')?.disable();
				}
			}
			else {
				this.showDistributorPanelError = true;
			}
		}
		else {
			this.addBaseProjectFormGroup.get('baseProjectPurpose')?.setValue('1');
			if(!this.baseProjectID || this.copyBaseProject){
				this.addBaseProjectFormGroup.get('baseProjectPeriodicity')?.setValue('');
				this.addBaseProjectFormGroup.get('baseProjectPeriodicity')?.enable();
			}
		}
	}

	resetDistributorPanelError(value: any, panelValue: any) {
		if (panelValue === 3) {
			this.showDistributorPanelError = true;
		}
		else {
			if (value == 8) {
				if(!this.baseProjectID){
					this.addBaseProjectFormGroup.get('baseProjectPurpose')?.setValue('');
				}
				if(!this.baseProjectID || this.copyBaseProject){
					this.addBaseProjectFormGroup.get('baseProjectPeriodicity')?.setValue('');
					this.addBaseProjectFormGroup.get('baseProjectPeriodicity')?.enable();
				}
			}
			else if (value == 11) {
				this.addBaseProjectFormGroup.get('baseProjectPurpose')?.setValue('1');
				if(!this.baseProjectID || this.copyBaseProject){
					this.addBaseProjectFormGroup.get('baseProjectPeriodicity')?.setValue('');
					this.addBaseProjectFormGroup.get('baseProjectPeriodicity')?.enable();
				}
			}
		}
	}

	resetDataTypeState() {
		this.showDistributorPanelError = false;
		this.addBaseProjectFormGroup.get('baseProjectPurpose')?.setValue('');
		this.addBaseProjectFormGroup.get('baseProjectPeriodicity')?.setValue('');
		this.addBaseProjectFormGroup.get('baseProjectPeriodicity')?.enable();
	}


	openUnsavedChangesModal(): void {
		this.UnsavedChangesModal = true;
	}

	closeUnsavedChangesModal(): void {
		this.UnsavedChangesModal = false;
	}

	canDeactivate(): Observable<boolean> | Promise<boolean> | boolean {
		if (this.unsavedChanges) {
			this.openUnsavedChangesModal();
			return false;
		}
		return true;
	}

	handleUnsavedChanges(event: boolean): void {
		if (event) {
			this.closeUnsavedChangesModal();
			this.saveChanges();
			if (this.pendingRoute) {
				this.router.navigateByUrl(this.pendingRoute);
				this.pendingRoute = null;
			}
			else {
				this.location.back();
			}
		}
		else {
			this.closeUnsavedChangesModal();
			this.pendingRoute = null;
		}
	}

	saveChanges(): void {
		if (this.addBaseProjectFormGroup.valid) {
			this.unsavedChanges = false;
			this.closeUnsavedChangesModal();
		} else {
			this.closeUnsavedChangesModal();
		}
	}

	ngOnDestroy() {
		if (this.unsavedChanges) {
			this.openUnsavedChangesModal()
		}
		this.subscription?.unsubscribe();
		window.removeEventListener('beforeunload', this.beforeUnloadHandler);
	}

	@HostListener('window:beforeunload', ['$event'])
	beforeUnloadHandler(event: any): void {
		if (this.unsavedChanges) {
			event.preventDefault();  // This prevents the default action (navigation)
			// Older way but still required for some browsers to display a confirmation dialog
			event.returnValue = '';  // An empty string is sufficient in most cases
		}
	}


	ngAfterContentChecked() {
		this.cdRef.detectChanges();
	}

	afterChildComponentsLoaded() {
		this.tabChange('bpSettings');
	}

	removeLocalStorageItems() {
		localStorage.setItem('baseProjectPageNo', JSON.stringify(1));
	}

	navigationToCopyBaseProject() {
		this.router.navigate(['/base-projects/copy/' + this.baseProjectID]);
	}

	navigateToIRSeparationAdd() {
		this.router.navigate(['/retailer-separation/create'], {
			queryParams: { sourceBPID: this.baseProjectID }
		});
	}


	tabChange(tabValue: string): void {
		if (tabValue === 'bcrConflicts' && !this.showBCRConflictsTab) {
			return;
		}
		this.selectedTab = tabValue;
		switch (tabValue) {
			case 'qcProjectSettings':
				this.handleQcProjectSettingsTab();
				break;
			case 'qcPeriods':
				this.handleQcPeriodsTab();
				break;
			case 'bpSecurity':
				this.handleBpSecurityTab();
			break;
			case 'qcSecurity':
				this.handleQcSecurityTab();
				break;
			case 'bcrConflicts':
				break;
		}
	}



	handleQcProjectSettingsTab() {
		this.showSQCFields = this.addBaseProjectFormGroup.get('baseProjectPanel')?.value != 2;
		this.showAutoLoadFields = this.addBaseProjectFormGroup.get('baseProjectType')?.value != 3;

		setTimeout(() => {
			if (!this.userRole) {
				this.disableQCProjectEdit = true;
				this.disableQCProjectEditField = true;
			}
			else {
				this.disableQCProjectEditField = false;
				this.disableQCProjectEdit = false;
			}
		}, 100);
	}

	handleQcPeriodsTab() {
		if (this.allRows()?.filter((row) => row.isSelected).length) {
			this.clearSelection();
		}
		if(GetApiService.loadQCPeriodTableData){
			setTimeout(() => {
				this.loadQCPeriodTableData = true;
			});
		}
	}

	handleQcSecurityTab() {
		if (this.allRows()?.filter((row) => row.isSelected).length) {
			this.clearSelection();
		}
		if(GetApiService.loadQCSecurityTableData){
			setTimeout(() => {
				this.loadQCSecurityTableData = true;
			});
		}
	}

  	handleBpSecurityTab() {
		if (this.allRows()?.filter((row) => row.isSelected).length) {
			this.clearSelection();
		}
	}

	panelChangeEvent(event: any, panelInputChange?: any) {
		this.filteredDataTypeData = this.handleDataTypeDropdownListForEdit(event);
		if (event == 1 || event == 2) {
			this.handlePanelChangeForType1And2();
		}
		else if (event == 3) {
			this.handlePanelChangeForType3();
		}
		this.addBaseProjectFormGroup.get('baseProjectType')?.updateValueAndValidity();
		setTimeout(() => {
			this.getValueByBaseProjectCountry(
				this.addBaseProjectFormGroup.get('baseProjectCountry')?.value,
				this.addBaseProjectFormGroup.get('baseProjectPanel')?.value,
				panelInputChange
			);
		}, 100);
	}

	handlePanelChangeForType1And2() {
		this.hideQCTabs = false;
		this.hideFieldsForDistributor = false;
		this.addBaseProjectFormGroup.get('baseProjectType')?.setValidators([Validators.required]);
		if (!this.baseProjectID) {
			this.addBaseProjectFormGroup.get('baseProjectPeriodicity')?.setValue('');
		}
		if (!this.baseProjectID || this.copyBaseProject) {
			this.addBaseProjectFormGroup.get('baseProjectDataType')?.setValue('');
			this.showDistributorPanelError = false;
			this.showDataTypeError = false;
		}

		if (this.baseProjectID && this.copyBaseProject) {
			this.addBaseProjectFormGroup.get('baseProjectType')?.setValue(this.data.typeId);
		}
	}

	handlePanelChangeForType3() {
		this.hideQCTabs = true;
		this.hideFieldsForDistributor = true;
		this.addBaseProjectFormGroup.get('baseProjectType')?.clearValidators();
		this.addBaseProjectFormGroup.get('baseProjectType')?.setValue(null);
		if (!this.baseProjectID || this.copyBaseProject) {
			this.addBaseProjectFormGroup.get('baseProjectDataType')?.setValue('');
			this.addBaseProjectFormGroup.get('baseProjectPeriodicity')?.setValue('2');
		}
	}

	getDataTypeList() {
		this.dataType$ = this.getApiService.getAsyncDataType()
			.pipe(
				map((dataTypes: any) => dataTypes.map((dataType: any) => ({ label: dataType.name, value: (dataType.id).toString() } as AutocompleteOption)))
			);
		this.dataType$.subscribe({
			next: (result) => {
				if (!this.baseProjectID) {
					const index = result.findIndex(item => item.value == '8');
					result.splice(index, 1);
				}
				this.dataTypeData = this.sortDataTypes(result);
			}
		});
	}

	/**
	 * Sort data types with custom order: FW, WC, MC, ME, LP, LPAC, LPWC, MP, MD, WE, Unknown
	 * Pushes rarer types to the end
	 */
	private sortDataTypes(dataTypes: AutocompleteOption[]): AutocompleteOption[] {
		const dataTypeOrder = ['FW', 'WC', 'MC', 'ME', 'LP', 'LPAC', 'LPWC', 'MP', 'MD', 'WE', 'Unknown'];

		return dataTypes.sort((a, b) => {
			const indexA = dataTypeOrder.indexOf(a.label);
			const indexB = dataTypeOrder.indexOf(b.label);
			if (indexA !== -1 && indexB !== -1) {
				return indexA - indexB;
			}

			if (indexA !== -1) return -1;
			if (indexB !== -1) return 1;

			return a.label.localeCompare(b.label);
		});
	}

	getPurposeList() {
		this.purpose$ = this.getApiService.getAsyncPurpose()
			.pipe(
				map((purposeList: any) => purposeList.map((purpose: any) => ({ label: purpose.name, value: (purpose.id).toString() } as AutocompleteOption)))
			);
		this.purpose$.subscribe({
			next: (result) => {
				if(!this.baseProjectID){
					const unknownIndex = result.findIndex(item => item.value == '5');
					result.splice(unknownIndex, 1);
					const deletedIndex = result.findIndex(item => item.value == '9');
					result.splice(deletedIndex, 1);
				}
				this.purposeData = result;
			}
		});
	}

	getCountryList() {
		this.countries$ = this.getApiService.getCountries()
			.pipe(
				map((countries: any) => countries.map((country: any) => ({ label: country.name, value: (country.id).toString(), isoName: country.isoName } as AutocompleteOption)))
			);
		this.countries$.subscribe({
			next: (result) => {
				this.countriesList = result;
			}
		});
	}

	getPeriodicityList() {
		this.periodicities$ = this.getApiService.getAsyncPeriodicities()
			.pipe(
				map((periodicities: any) => periodicities.map((periodicity: any) => ({ label: periodicity.name, value: (periodicity.id).toString() } as AutocompleteOption)))
			);
		this.periodicities$.subscribe({
			next: (result) => {
				this.periodicityData = this.sortPeriodicities(result);
				this.periodicityDataForFilter = [...this.periodicityData];
			}
		});
	}

	/**
	 * Sort periodicities in ascending range-wise order: daily, weekly, monthly, 2 monthly, 3 monthly, 4 monthly, etc.
	 * Within each group, sort by starting month: Jan, Feb, Mar, etc.
	 */
	private sortPeriodicities(periodicities: AutocompleteOption[]): AutocompleteOption[] {
		return periodicities.sort((a, b) => {
			const orderA = this.getPeriodicityOrder(a.label);
			const orderB = this.getPeriodicityOrder(b.label);

			if (orderA !== orderB) {
				return orderA - orderB;
			}
			const monthOrderA = this.getStartingMonthOrder(a.label);
			const monthOrderB = this.getStartingMonthOrder(b.label);

			if (monthOrderA !== monthOrderB) {
				return monthOrderA - monthOrderB;
			}
			return a.label.localeCompare(b.label);
		});
	}

	
	/**
	 * Get the sort order for periodicity based on frequency
	 */
	private getPeriodicityOrder(label: string): number {
		const lowerLabel = label.toLowerCase();

		// Daily periodicities
		if (lowerLabel.includes('daily')) {
			return 1;
		}

		// Weekly periodicities
		if (lowerLabel.includes('weekly')) {
			return 7;
		}

		if (lowerLabel.includes('monthly')) {
			const monthMatch = lowerLabel.match(/(\d+)-monthly/);
			if (monthMatch) {
				const monthCount = parseInt(monthMatch[1], 10);
				return monthCount * 30; // 2-monthly = 60, 3-monthly = 90, etc.
			}
			return 30;
		}
		if (lowerLabel.includes('yearly')) {
			return 360;
		}

		return 1000;
	}

	/**
	 * Get the sort order for starting month within the same periodicity group
	 */
	private getStartingMonthOrder(label: string): number {
		const lowerLabel = label.toLowerCase();

		const monthMatch = lowerLabel.match(/start\s+(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/);
		if (monthMatch) {
			const monthMap: { [key: string]: number } = {
				'jan': 1, 'feb': 2, 'mar': 3, 'apr': 4, 'may': 5, 'jun': 6,
				'jul': 7, 'aug': 8, 'sep': 9, 'oct': 10, 'nov': 11, 'dec': 12
			};
			return monthMap[monthMatch[1]] || 0;
		}
		return 0;
	}

	getProjectSubTypeList() {
		this.projectSubType$ = this.getApiService.getAsyncProjectSubType()
			.pipe(
				map((projectSubTypes: any) => projectSubTypes.map((projectSubType: any) => ({ label: projectSubType.name, value: (projectSubType.id).toString() } as AutocompleteOption)))
			);
		this.projectSubType$.subscribe({
			next: (result) => {
				result.forEach((item: any) => {
					if (item.value == 1 || item.value == 2 || item.value == 3) {
						this.baseProjectTypeOptions.push(
							{ title: item.label, inputClass: '', name: 'radioGroup', value: item.value, hasError: false, checked: false, disabled: false }
						);
					}
				});
				this.projectSubTypeList = result;
			}
		});
	}

	getPanelList() {
		this.panels$ = this.getApiService.getAsyncPanel()
			.pipe(
				map((panelList: any) => panelList.map((panel: any) => ({ label: panel.name, value: (panel.id).toString() } as AutocompleteOption)))
			);
		this.panels$.subscribe({
			next: (result) => {
				result.forEach((item: any) => {
					this.baseProjectPanelOptions.push(
						{ title: item.label, inputClass: '', name: 'radioGroup', value: item.value, hasError: false, checked: false, disabled: false }
					);
				});
				this.panelList = result;
			}
		});
	}

	getValueByBaseProjectCountry(countryId: any, panelId: any, panelInputChange?: any) {
		const countryIds: any = [];
		countryIds.push(countryId);
		if (countryId && panelId) {
			if(!this.baseProjectID || this.copyBaseProject){
				this.resetFormValues();
			}
			this.loadProductGroups(panelId, countryIds, panelInputChange);
			if (panelId != 3) {
				this.loadBaseProjects(countryId, panelId, panelInputChange);
			}
		}
	}

	resetFormValues() {
		this.addBaseProjectFormGroup.controls.baseProjectProductGroup.setValue([]);
		this.addBaseProjectFormGroup.controls.baseProjectPredecessor.setValue([]);
	}

	loadProductGroups(panelId: any, countryIds: any, panelInputChange?: any) {
		this.productGroup$ = this.postApiService.getAsyncProductGroup([panelId], countryIds, [], [], [])
			.pipe(
				map((productGroups: any) => productGroups.map((productGroup: any) => (
					{
						label: productGroup.description + ' (' + productGroup.id + ')',
						value: (productGroup.id).toString(),
					}
				)))
			);
		this.productGroup$.subscribe({
			next: result => this.handleProductGroupsResponse(result, panelInputChange),
			error: error => this.handleErrorForLoadProductGroups(error),
		});
	}

	handleProductGroupsResponse(result: any, panelInputChange?: any) {
		this.productGroupData = result;
		if (this.baseProjectID && this.productGroupCount < 1) {
			const selectedProductGroups: any = [];
			this.selectedProductGroupLabels = [];
			this.data.productGroups.forEach((item: any) => { selectedProductGroups.push(item.productGroupId.toString()) });
			this.addBaseProjectFormGroup.controls.baseProjectProductGroup.setValue(selectedProductGroups);
			this.productGroupData.forEach((productGroup: any) => {
				selectedProductGroups.forEach((selectedPGs: any) => {
					if (productGroup.value == selectedPGs) {
						this.selectedProductGroupLabels.push(productGroup.label);
					}
				})
			});
			this.productGroupCount++;
		}
		if(panelInputChange){
			this.resetFormValues();
		}
	}

	handleErrorForLoadProductGroups(error: any): void {
		const title = this.getErrorTitle(error, 'Product-Groups');
		this.notifyWidget(title, 'error');
	}

	loadBaseProjects(countryId: any, panelId: any, panelInputChange?: any) {
		this.baseProject$ = this.postApiService.getAsyncBaseProjectsByCountryID(countryId, panelId)
			.pipe(
				map((baseProjects: any) => baseProjects.map((baseProject: any) => ({ label: baseProject.name + ' (' + baseProject.id + ')', value: (baseProject.id).toString() } as AutocompleteOption)))
			);
		this.baseProject$.subscribe({
			next: result => this.handleLoadBaseProjectsResponse(result, panelInputChange),
			error: error => this.handleLoadBaseProjectsError(error),
		});
	}

	handleLoadBaseProjectsResponse(result: any, panelInputChange?: any) {
		if (this.baseProjectID) {
			const filteredResult = result.filter((item: any) => item.value != this.baseProjectID);
			this.baseProjectData = filteredResult;
			const selectedPredecessors: any = [];
			this.selectedPredecessorBaseProjectLabels = [];
			this.data.predecessors.forEach((item: any) => { selectedPredecessors.push(item.predecessorId.toString()) });
			this.addBaseProjectFormGroup.controls.baseProjectPredecessor.setValue(selectedPredecessors);
			this.baseProjectData.forEach((bpData: any) => {
				selectedPredecessors.forEach((selectedBPs: any) => {
					if (bpData.value == selectedBPs) {
						this.selectedPredecessorBaseProjectLabels.push(bpData.label);
					}
				})
			});
		}
		else {
			this.baseProjectData = result;
		}
		if(panelInputChange){
			this.resetFormValues();
		}
	}

	handleLoadBaseProjectsError(error: any) {
		if (error.status === 404) {
			this.baseProjectData = [];
		}
		else {
			this.handleErrorForPredecessorBaseProjects(error);
		}
	}

	handleErrorForPredecessorBaseProjects(error: any): void {
		const title = this.getErrorTitle(error, 'Predecessor Base Projects');
		this.notifyWidget(title, 'error');
	}

	getErrorTitle(error: any, context: string): string {
		const errorCodes = [400, 401, 404, 500, 502, 503, 504];
		if (errorCodes.includes(error.status)) {
			return `Error: ${error.status}. Unable to load ${context}. Please contact administrator.`;
		}
		return error?.message == null ? error?.error : error?.message;
	}

	getBaseProjectByBaseProjectId(baseProjectId: number) {
		this.fetchBPData = true;
		this.getApiService.getBaseProjectsByBaseProjectId(baseProjectId).subscribe({
			next: (result) => {
				this.data = result;
				this.isTypeIdValid = this.data.typeId === 3;
				this.isIRSeparationRequested = this.data.isRetailerSeparationRequested;
				this.isDeleted = this.data.deleted;
				this.disableField = false;
				this.bindBaseProjectDataForEdit(this.data);
				this.calculateRefreshedDateTime();
				this.getBaseProjectForQCPeriod();
			},
			error: (error) => {
				let title: string;
				if (
					error.status == 400 ||
					error.status == 401 ||
					error.status == 500 ||
					error.status == 502 ||
					error.status == 503 ||
					error.status == 504
				) {
					title = 'Error: ' + error.status + '. Unable to load Base Project Data. Please contact administrator.';
					this.notifyWidget(title, 'error');
				}
				else if (error.status == 404) {
					title = 'No Base Project exists with Id ' + this.baseProjectID;
					this.notifyWidget(title, 'error');
					this.router.navigate(['/base-projects/list']);
				}
				else if (error.status == 403) {
					this.hideForm = true;
				}
			},
		});
	}

	addBaseProjectFormInitializer() {
		this.addBaseProjectFormGroup = this.formBuilder.group({
			baseProjectPanel: null,
			baseProjectType: null,
			baseProjectDataType: null,
			baseProjectPurpose: !this.baseProjectID ? '1' : null,
			// baseProjectPurpose: (!this.baseProjectID || (this.baseProjectID && !this.copyBaseProject)) ? '1' : null, 
			baseProjectPeriodicity: null,
			baseProjectCountry: null,
			baseProjectPredecessor: [[], { nonNullable: true }],
			baseProjectProductGroup: [[], { nonNullable: true }],
			baseProjectName: [{ value: '', disabled: true }],
			combinedName: ['', Validators.maxLength(40)],
			suffixes: ['', Validators.maxLength(40)]
		});
	}

	bindBaseProjectDataForEdit(baseProjectData: any) {
		
		this.handlePanelChange(baseProjectData.panelId);
		this.patchBaseProjectForm(baseProjectData);
		this.setInitialCheckedOptions(baseProjectData);
		this.checkCopyOrUpdateState();
		this.splitBaseProjectName(baseProjectData.name, baseProjectData.id);
		this.setupValueChangeSubscription();
		if (!this.copyBaseProject) {
			this.checkDataType();
		}
		if (this.copyBaseProject || this.editBaseProject) {
			this.filterPeriodicityList(baseProjectData.dataTypeId);
		}
		this.checkUserRole();
		this.setupQCProjectSettings(baseProjectData);
		if(this.editBaseProject){
			setTimeout(() => {
				this.filteredDataTypeData = this.handleDataTypeDropdownListForEdit(baseProjectData.panelId, baseProjectData.periodicityId);
			}, 1000);
		}
	}

	patchBaseProjectForm(baseProjectData: any) {
		this.addBaseProjectFormGroup.patchValue({
			baseProjectPanel: baseProjectData.panelId,
			baseProjectType: baseProjectData.typeId,
			baseProjectPeriodicity: baseProjectData.periodicityId.toString(),
			baseProjectCountry: baseProjectData.countryId.toString(),
			baseProjectPredecessor: baseProjectData.predecessors,
			baseProjectProductGroup: baseProjectData.productGroups,
			baseProjectName: baseProjectData.name,
			baseProjectDataType: baseProjectData.dataTypeId,
			baseProjectPurpose: baseProjectData.purposeId,
		});
		this.initialbaseProjectProductGroup=[];
		this.addBaseProjectFormGroup.get('baseProjectProductGroup')?.value.forEach((item: any) => {
			this.initialbaseProjectProductGroup.push(item);
		});
		this.pageLoad = false;
		if(!this.fetchBPData){
			this.handleDataTypeChangeForNewProject(baseProjectData.dataTypeId);
		}
		this.handleDataTypeChangeForExistingProject(baseProjectData.dataTypeId);
		setTimeout(() => {
			this.fetchBPData = false;
		}, 1000);
	}

	setInitialCheckedOptions(baseProjectData: any) {
		setTimeout(() => {
			this.baseProjectPanelOptions.forEach(item => {
				item.checked = item.value == baseProjectData.panelId;
			});
			this.baseProjectTypeOptions.forEach(item => {
				item.checked = item.value == baseProjectData.typeId;
			});
		}, 1500);
	}

	handlePanelChange(panelId: number) {
		this.panelChangeEvent(panelId);
		if (this.addBaseProjectFormGroup.get('baseProjectPanel')?.value == 3 || !this.data?.qcProjects) {
			this.hideQCTabs = true;
		}
	}

	checkCopyOrUpdateState() {
		if (this.router.url.indexOf('copy') > -1) {
			this.copyBaseProject = true;
		}
		if (this.router.url.indexOf('update') > -1) {
			this.editBaseProject = true;
			if(this.userRole?.name !== 'Master'){
				this.addBaseProjectFormGroup.get('baseProjectPurpose')?.disable();
			}
		}
	}

	splitBaseProjectName(name: string, id: number) {
		if (id > 10000000) {
			const [part1, part2] = this.splitStringAtDash(name);
			this.addBaseProjectFormGroup.get('baseProjectName')?.setValue(part1);
			this.addBaseProjectFormGroup.get('suffixes')?.setValue(part2);
		}
		else {
			const [part1, part2] = this.splitStringAtSpace(name);
			this.addBaseProjectFormGroup.get('baseProjectName')?.setValue(part1);
			this.addBaseProjectFormGroup.get('suffixes')?.setValue(part2);
		}
	}

	splitStringAtDash(input: string): [string, string] {
		const substrings = [
			'1d', '1w', '2m2', '4m2', '1y', '2m', '1m', '4m', 'y2', '3m', '6m', '9m4',
			'4m3', '6m3', '7m', '5m8', '4m4', '6m2', '3m2', '5m', '8m', '9m', '10m', '11m'
		];

		let firstMatchIndex = -1;
		let firstMatchLength = 0;

		// Find the first occurrence of any of the substrings followed by a space
		for (const substr of substrings) {
			const regex = new RegExp(`${substr} `, 'g');
			const match = regex.exec(input);
			if (match && (firstMatchIndex === -1 || match.index < firstMatchIndex)) {
				firstMatchIndex = match.index;
				firstMatchLength = match[0].length;
			}
		}

		if (firstMatchIndex !== -1) {
			const splitIndex = firstMatchIndex + firstMatchLength - 1;
			const part1 = input.substring(0, splitIndex);
			const part2 = input.substring(splitIndex + 1);
			return [part1, part2];
		}

		return [input, ''];
	}

	splitStringAtSpace(input: string): [string, string] {
		const substrings = [
			'1d', '1w', '2m2', '4m2', '1y', '2m', '1m', '4m', 'y2', '3m', '6m', '9m4',
			'4m3', '6m3', '7m', '5m8', '4m4', '6m2', '3m2', '5m', '8m', '9m', '10m', '11m'
		];
		let lastMatchIndex = -1;
		let lastMatchLength = 0;

		// Find the last occurrence of any of the substrings followed by a space
		for (const substr of substrings) {
			const regex = new RegExp(`${substr} `, 'g');
			let match;
			while ((match = regex.exec(input)) !== null) {
				if (match.index > lastMatchIndex) {
					lastMatchIndex = match.index;
					lastMatchLength = match[0].length;
				}
			}
		}

		if (lastMatchIndex !== -1) {
			const splitIndex = lastMatchIndex + lastMatchLength - 1;
			const part1 = input.substring(0, splitIndex);
			const part2 = input.substring(splitIndex + 1);
			return [part1, part2];
		}

		return [input, ''];
	}


	setupValueChangeSubscription() {
		this.addBaseProjectFormGroup.valueChanges.subscribe(value => {
			if (value) {
				this.unsavedBPChangesValue(value);
			}
		});
	}

	unsavedBPChangesValue(value: any) {
		const bpData = {
			"baseProjectDataType": this.data.dataTypeId,
			"baseProjectPurpose": this.data.purposeId,
			"baseProjectProductGroup": this.data.productGroups,
			"baseProjectPredecessor": this.data.predecessors,
			"baseProjectName": this.data.name,
		}
		const bpProductGroup = value.baseProjectProductGroup.map((item: any) => ({
            productGroupId: typeof item === 'string' ? parseInt(item) : item
        }));
        
        const bpPredecessor = value.baseProjectPredecessor.map((item: any) => ({
            predecessorId: typeof item === 'string' ? parseInt(item) : item
        }));
		const newBPData = {
			"baseProjectDataType": value.baseProjectDataType!=null? parseInt(value.baseProjectDataType):parseInt(this.data.dataTypeId),
			"baseProjectPurpose": value.baseProjectPurpose!=null? parseInt(value.baseProjectPurpose):parseInt(this.data.purposeId),
			"baseProjectProductGroup": bpProductGroup,
			"baseProjectPredecessor": bpPredecessor,
			"baseProjectName": value.combinedName,
		}
		if (JSON.stringify(bpData) === JSON.stringify(newBPData)) {
			this.unsavedChanges = false;
		}
		else {
			this.unsavedChanges = true;
		}
	}

	checkDataType() {
		const dataType = this.addBaseProjectFormGroup.get('baseProjectDataType')?.value;
		if(this.userRole?.name !== 'Master'){
			this.addBaseProjectFormGroup.get('baseProjectDataType')?.disable();
		}
	}

	checkUserRole() {
		if (this.userRole?.name !== 'Master') {
			this.disableBPEdit = true;
		}
		else {
			this.disableBPEdit = false;
		}
	}

	setupQCProjectSettings(baseProjectData: any) {
		if (baseProjectData.qcProjects) {
			const qcProjectSettingsData = baseProjectData.qcProjects;
			this.qcProjectSettingsFormGroup.patchValue({
				sqcMode: qcProjectSettingsData.sqcMode,
				// isAutomatedPriceCheck: qcProjectSettingsData.isAutomatedPriceCheck,
				// isAutoLoad: qcProjectSettingsData.isAutoLoad,
				resetCorrectionTypeId: qcProjectSettingsData.resetCorrectionTypeId ? qcProjectSettingsData.resetCorrectionTypeId.toString() : null,
			});
			this.isAutomatedPriceCheck = qcProjectSettingsData.isAutomatedPriceCheck;
			this.isAutoLoad = qcProjectSettingsData.isAutoLoad;
			this.setQCOptions(qcProjectSettingsData);
			this.qcProjectID = qcProjectSettingsData.id;
			if (this.qcProjectID) {
				this.setupQCProjectData();
			}
		}
	}

	setQCOptions(qcProjectSettingsData: any) {
		this.sqcModeOptions.forEach((item: any) => {
			if (item.value == qcProjectSettingsData.sqcMode) {
				item.checked = true;
			}
		});
	}

	setupQCProjectData() {
		const periodicityId = this.data.periodicityId;
		this.getPeriodListByPeriodicityId(periodicityId, 10000000);
		this.getAsyncCurrentPeriodsByPeriodicity(periodicityId);
		this.getQCProjectAssignedUsers();
		this.qcProjectSettingsFormGroup.valueChanges.subscribe(value => {
			if (value) {
				this.unsavedQCChangesValue(value);
			}
		});
	}

	unsavedQCChangesValue(value: any) {
		const qcProjectData = {
			"sqcMode": this.data?.qcProjects?.sqcMode?.toString() || "",
			"isAutomatedPriceCheck": this.data?.qcProjects?.isAutomatedPriceCheck || "",
			"isAutoLoad": this.data?.qcProjects?.isAutoLoad || "",
			"resetCorrectionTypeId": (this.data?.qcProjects?.resetCorrectionTypeId != null) ? this.data.qcProjects.resetCorrectionTypeId.toString() : "" // Added default value
		};
		const newQCProjectData = {
			"sqcMode": value.sqcMode?.toString() || "",
			"isAutomatedPriceCheck": this.isAutomatedPriceCheck || "",
			"isAutoLoad": this.isAutoLoad || "",
			"resetCorrectionTypeId": (value.resetCorrectionTypeId != null) ? value.resetCorrectionTypeId.toString() : ""
		};
		if (JSON.stringify(qcProjectData) === JSON.stringify(newQCProjectData)) {
			this.unsavedChanges = false;
		}
		else {
			this.unsavedChanges = true;
		}
	}

	handleDataTypeDropdownListForEdit(panelId?: number, periodicityValue?: number){
		let valuesToFilter: string[];
		if(panelId == 3){
			valuesToFilter = ['5', '6', '7', '8'];
		}
		else{
			valuesToFilter = [];
		}
		if(panelId != 3 && periodicityValue){
			if (periodicityValue === 2) {
				valuesToFilter = ['1', '2', '4', '5', '6', '7', '8', '9', '10'];
			}
			else if (periodicityValue === 4) {
				valuesToFilter = ['3', '4', '7', '8', '9', '11'];
			}
			else {
				valuesToFilter = ['4', '7', '9', '8', '11'];
			}
		}
		const filteredData = this.dataTypeData.filter((item: AutocompleteOption) => valuesToFilter.includes(item.value));
		return this.sortDataTypes(filteredData);
	}

	generateBaseProjectName() {
		if (this.hasInvalidBPFormValues()) {
			this.showError = true;
		}
		else {
			this.loadTableData = true;
			this.showError = false;
			this.postApiService.getShortDescriptionForBaseProjectName(
				this.getFormValue('baseProjectPeriodicity'),
				this.getFormValue('baseProjectProductGroup')
			).subscribe({
				next: (result: any) => this.handleApiSuccessForBaseProjectName(result),
				error: (error) => this.handleApiErrorForBaseProjectName(error)
			});
		}
	}

	hasInvalidBPFormValues(): boolean {
		return !this.getFormValue('baseProjectPeriodicity') ||
			!this.getFormValue('baseProjectCountry') ||
			!this.getFormValue('baseProjectProductGroup')?.length;
	}

	getFormValue(controlName: string) {
		return this.addBaseProjectFormGroup.get(controlName)?.value;
	}

	handleApiSuccessForBaseProjectName(result: any) {
		const selectedCountry = this.getSelectedCountry();
		const { sector, dpgs } = this.getSectorAndDpgs(result);
		const autoGeneratedBaseProjectName = selectedCountry.isoName + '_' + sector + ' ' + dpgs + ' ' + result.periodicityShortDesc;
		this.setBaseProjectName(autoGeneratedBaseProjectName);
		this.disableAutoGenerateName = false;
		this.firsttime= true;
		this.loadTableData = false;
		this.updateCombinedName();
	}

	getSelectedCountry() {
		return this.countriesList.find((item: AutocompleteOption) => this.getFormValue('baseProjectCountry') == item.value);
	}

	getSectorAndDpgs(result: any) {
		let sector = result.domainProductGroups[0].sectorSDesc.substr(0, 5);
		let dpgs = result.domainProductGroups[0].domainProductGroupDDesc.substr(0, 15);
		if (result.domainProductGroups.length > 1) {
			const uniqueSectors = new Set(result.domainProductGroups.map(dpg => dpg.sectorSDesc));
			dpgs = result.domainProductGroups[0].domainProductGroupDDesc.substr(0, 12) + '(+)';
			if (uniqueSectors.size > 1) {
				sector = result.domainProductGroups[0].sectorSDesc.substr(0, 2) + '(+)';
			}
		}

		return { sector, dpgs };
	}

	setBaseProjectName(name: string) {
		this.addBaseProjectFormGroup.get('baseProjectName')?.setValue(name);
	}

	handleApiErrorForBaseProjectName(error: any) {
		const title = this.getErrorMessageForBaseProjectName(error);
		this.notifyWidget(title, 'error');
		this.loadTableData = false;
	}

	getErrorMessageForBaseProjectName(error: any): string {
		if ([400, 401, 404, 500, 502, 503, 504].includes(error.status)) {
			return `Uh-oh! Name Generation encountered an issue. Please try again. Error: ${error.status}`;
		}
		return error?.message == null ? error?.error : error?.message;
	}

	isCopyingBaseProject(): boolean {
		return this.router.url.indexOf('copy') > -1;
	}

	saveBaseProject() {
		if (!this.addBaseProjectFormGroup.get('baseProjectName')?.value) {
			this.addBaseProjectFormGroup.get('baseProjectName')?.setValidators([Validators.required]);
			this.addBaseProjectFormGroup.get('baseProjectName')?.updateValueAndValidity();
		} else {
			this.loadTableData = true;
			if (this.isCopyingBaseProject()) {
				this.createBaseProject();
				this.unsavedChanges = false;
			} else {
				if (this.baseProjectID && this.editBaseProject) {
					this.updateBaseProjectBCRCheck();
				} else {
					this.createBaseProject();
				}
			}
		}
	}

	createBaseProject() {
		const baseProjectProductGroup = this.buildProductGroup();
		const productGroupIds = baseProjectProductGroup.map((item: any) => parseInt(item.productGroupId));
		this.bcrDetailList = [];
		this.visibleBCRDetailList = [];
		this.checkBCRConflicts(productGroupIds, baseProjectProductGroup,"Create");
		if (!this.bcrDetailList || this.bcrDetailList.length === 0) {
			return;
		}
	}

	buildProductGroup() {
		const baseProjectProductGroup: any = [];
		this.addBaseProjectFormGroup.get('baseProjectProductGroup')?.value.forEach((item: any) => {
			baseProjectProductGroup.push({ productGroupId: item });
		});
		return baseProjectProductGroup;
	}

	checkBCRConflicts(productGroupIds: any, baseProjectProductGroup: any,action:string) {
		this.postApiService.baseChannelRearrancementCheck({ productGroupIds: productGroupIds })
			.subscribe({
				next: (response) => this.handleBCRCheckResponse(response, baseProjectProductGroup,action),
				error: (error) => this.handleBCRCheckError(error,action)
			});
	}

	copyBcrConflictsLink() {
		if (!this.bcrDetailList || this.bcrDetailList.length === 0) {
			this.notifyWidget('No BCR conflicts available', 'info', 'Please ensure there are conflicts to generate the link.');
			return;
		}
		this.loadTableData = true;
		const baseUrl = window.location.origin;
		const uniqueId = new Date().getTime();
		const link = `${baseUrl}/bcr/${uniqueId}`;
		const payload = {
			typeId: 1,
			data: JSON.stringify(this.bcrDetailList),
			link: link,
		};
		this.postApiService.addInfoDetails(payload).subscribe({
			next: (response: any) => {
				const generatedLink = `${baseUrl}/bcr/${response.id}`;
				this.copyToClipboard(generatedLink);
				this.loadTableData = false;
			},
			error: () => {
				this.notifyWidget('Failed to generate the link', 'error', 'There was an error while generating the link.');
				this.loadTableData = false;
			},
		});
	}

	copyToClipboard(generatedlink: any) {
		navigator.clipboard.writeText(generatedlink).then(
			() => {
				this.notifyWidget('Success!', 'success', 'Link copied to clipboard.');
			},
			() => {
				this.notifyWidget('Copy Failed', 'error', 'Could not copy the link to clipboard.');
			}
		);
	}

	handleBCRCheckResponse(response: any, baseProjectProductGroup: any,action:string) {
		if (response.conflicts.length) {
			if(action=="Update"){
				this.showBCRConflictsTabEdit = true;
			}
			else{
				this.showBCRConflictsTab = true;
			}
			this.bcrConflictsCount = response.numberOfConflicts;
			this.bcrDetailList = response.conflicts;
			this.currentPageForBCRDetail = 1;
			this.visibleBCRDetailList = this.getPageBCRDetail(this.currentPageForBCRDetail, this.pageSizeForBCRDetail);
			this.selectedTab = 'bcrConflicts';
			this.bpValidationModal = true;
			this.openBPValidationModal();
			this.loadTableData = false;
		} else {
			this.showBCRConflictsTab = false;
			this.showBCRConflictsTabEdit = false;
			if(action=="Update"){
				const baseProjectProductGroup : any= [];
				this.addBaseProjectFormGroup.get('baseProjectProductGroup')?.value.forEach((item: any) => {
					baseProjectProductGroup.push({ productGroupId: parseInt(item) });
				});
				if(this.initialbaseProjectProductGroup.length>baseProjectProductGroup.length){
					this.openpgEditQCPeriodValidationModal();
				}
				else if(this.initialbaseProjectProductGroup.length==baseProjectProductGroup.length 
					&& JSON.stringify(this.initialbaseProjectProductGroup)!=JSON.stringify(baseProjectProductGroup)){
						this.openpgEditQCPeriodValidationModal();
				}
				else{
					this.updateBaseProject();
				}
			}
			else{
				this.createBaseProjectWithoutConflicts(baseProjectProductGroup);
			}
		}
	}


	handleBCRConflicts(response: any) {
		this.bcrConflictsCount = response.numberOfConflicts;
		this.bcrDetailList = response.conflicts;
		this.currentPageForBCRDetail = 1;
		this.visibleBCRDetailList = this.getPageBCRDetail(this.currentPageForBCRDetail, this.pageSizeForBCRDetail);
		this.openBPValidationModal();
	}

	createBaseProjectWithoutConflicts(baseProjectProductGroup: any) {
		const baseProjectPredecessor = this.buildPredecessorArray();
		let resetCorrectionTypeId, isAutoLoad, sqcMode, isAutomatedPriceCheck;
		if (!this.copyBaseProject) {
			({ resetCorrectionTypeId, isAutoLoad, sqcMode, isAutomatedPriceCheck } = this.handleQCProjectValuesForBPCreation());
		}
		this.handleBaseProjectCreationSubscription(baseProjectPredecessor, baseProjectProductGroup, resetCorrectionTypeId, isAutoLoad, sqcMode, isAutomatedPriceCheck);
	}

	buildPredecessorArray() {
		const baseProjectPredecessor: any = [];
		this.addBaseProjectFormGroup.get('baseProjectPredecessor')?.value.forEach((item: any) => {
			baseProjectPredecessor.push({ predecessorId: item });
		});
		return baseProjectPredecessor;
	}

	handleQCProjectValuesForBPCreation() {
		let resetCorrectionTypeId: any;
		let isAutoLoad: any;
		let sqcMode: any;
		let isAutomatedPriceCheck: any;
		if (this.addBaseProjectFormGroup.get('baseProjectPanel')?.value == 1 && this.addBaseProjectFormGroup.get('baseProjectType')?.value == 1) {
			isAutoLoad = false;
			resetCorrectionTypeId = 1;
			sqcMode = 0;
			isAutomatedPriceCheck = false;
		}
		if (this.addBaseProjectFormGroup.get('baseProjectPanel')?.value == 2 && this.addBaseProjectFormGroup.get('baseProjectType')?.value == 1) {
			isAutoLoad = false;
			resetCorrectionTypeId = 1;
			sqcMode = 0;
			isAutomatedPriceCheck = null;
		}
		if (this.addBaseProjectFormGroup.get('baseProjectPanel')?.value == 1 && this.addBaseProjectFormGroup.get('baseProjectType')?.value == 2) {
			isAutoLoad = true;
			resetCorrectionTypeId = 1;
			sqcMode = 0;
			isAutomatedPriceCheck = false;
		}
		if (this.addBaseProjectFormGroup.get('baseProjectPanel')?.value == 2 && this.addBaseProjectFormGroup.get('baseProjectType')?.value == 2) {
			isAutoLoad = true;
			resetCorrectionTypeId = 1;
			sqcMode = 0;
			isAutomatedPriceCheck = null;
		}
		if (this.addBaseProjectFormGroup.get('baseProjectPanel')?.value == 1 && this.addBaseProjectFormGroup.get('baseProjectType')?.value == 3) {
			isAutoLoad = null;
			resetCorrectionTypeId = null;
			sqcMode = 0;
			isAutomatedPriceCheck = false;
		}
		if (
			(this.addBaseProjectFormGroup.get('baseProjectPanel')?.value == 2 && this.addBaseProjectFormGroup.get('baseProjectType')?.value == 3) ||
			this.addBaseProjectFormGroup.get('baseProjectPanel')?.value == 3
		) {
			isAutoLoad = null;
			resetCorrectionTypeId = null;
			sqcMode = 0;
			isAutomatedPriceCheck = null;
		}
		return { resetCorrectionTypeId, isAutoLoad, sqcMode, isAutomatedPriceCheck };
	}

	handleBaseProjectCreationSubscription(baseProjectPredecessor: any, baseProjectProductGroup: any, resetCorrectionTypeId: any, isAutoLoad: any, sqcMode: any, isAutomatedPriceCheck: any) {
		const baseProjectPanelValue = this.addBaseProjectFormGroup.get('baseProjectPanel')?.value;
		const baseProjectPurposeValue = this.addBaseProjectFormGroup.get('baseProjectPurpose')?.value;
		const isRelevantForReportingEnabled = baseProjectPanelValue == 3 ? false : (baseProjectPurposeValue == 1 || baseProjectPurposeValue == 6);
		this.postApiService.createBaseProject(
			this.addBaseProjectFormGroup.get('combinedName')?.value,
			(this.addBaseProjectFormGroup.get('baseProjectPanel')?.value == 3) ? 0 : this.addBaseProjectFormGroup.get('baseProjectType')?.value,
			this.addBaseProjectFormGroup.get('baseProjectPanel')?.value,
			this.addBaseProjectFormGroup.get('baseProjectDataType')?.value,
			this.addBaseProjectFormGroup.get('baseProjectPurpose')?.value,
			this.addBaseProjectFormGroup.get('baseProjectPeriodicity')?.value,
			this.addBaseProjectFormGroup.get('baseProjectCountry')?.value,
			baseProjectPredecessor,
			baseProjectProductGroup,
			isRelevantForReportingEnabled,
			this.copyBaseProject ? this.qcProjectSettingsFormGroup.get('resetCorrectionTypeId')?.value : resetCorrectionTypeId,
			this.copyBaseProject ? this.qcProjectSettingsFormGroup.get('isAutoLoad')?.value : isAutoLoad,
			this.copyBaseProject ? this.qcProjectSettingsFormGroup.get('sqcMode')?.value : sqcMode,
			this.copyBaseProject ? this.qcProjectSettingsFormGroup.get('isAutomatedPriceCheck')?.value : isAutomatedPriceCheck
		).subscribe({
			next: (response) => this.handleBaseProjectCreationSuccess(response),
			error: (error) => this.handleBPCreationError(error)
		});
	}


	handleBaseProjectCreationSuccess(response: any) {
		this.baseProjectTypeOptions.forEach((item: any) => {
			item.disabled = true;
		});
		this.disableField = true;
		this.removeLocalStorageItems();
		const title = this.copyBaseProject ? 'Base Project Copied' : 'Base Project Created';
		const message = 'BP ID : ' + response.id;
		this.notifyWidget(title, 'success', message);
		setTimeout(() => {
			this.router.navigate(['/base-projects/list/']);
			this.loadTableData = false;
		}, 200);
	}

	handleBPCreationError(error: any) {
		let title: string;
		if (error.status == 400 || error.status == 401 || error.status == 404 || error.status == 502 || error.status == 503 || error.status == 504) {
			title = 'Error: ' + error.status + '. Base Project creation failed. Please contact administrator.';
			this.notifyWidget(title, 'error');
		}
		else if (error.status == 500) {
			if (error.error.exception.message.includes('BCR Conflicts found')) {
				this.openBPValidationModal()
				this.bpValidationMessage = 'Different definitions of Base Channel Rearrangements detected. Please correct the BCR definitions and try again. Number of conflicts: ' + error.error.exception.message.split('Number of conflicts')[1];
			}
			else {
				title = 'Error: ' + error.status + '. Base Project creation failed. Please contact administrator.';
				this.notifyWidget(title, 'error');
			}
		}
		else {
			title = error?.message == null ? error?.error : error?.message;
			this.notifyWidget(title, 'error');
		}
		this.loadTableData = false;
	}

	handleBCRCheckError(error: any,action:string) {
		let title: string;
		if ([400, 401, 404, 502, 503, 504].includes(error.status)) {
			title = 'Error: ' + error.status + '. Something went wrong. Please contact administrator.';
			this.notifyWidget(title, 'error');
		} else if (error.status == 500) {
			if (error.error.exception.message.includes('BCR Conflicts found')) {
				if(action=="Update"){
					this.showBCRConflictsTabEdit = true;
				}
				else{
					this.showBCRConflictsTab = true;
				}
				this.selectedTab = 'bcrConflicts';
				this.bpValidationMessage = 'Different definitions of Base Channel Rearrangements detected. Please correct the BCR definitions and try again. Number of conflicts: ' + error.error.exception.message.split('Number of conflicts')[1];
			} else {
				title = 'Error: ' + error.status + '. Something went wrong. Please contact administrator.';
				this.notifyWidget(title, 'error');
			}
		} else {
			title = error?.message == null ? error?.error : error?.message;
			this.notifyWidget(title, 'error');
		}
		this.loadTableData = false;
	}

	closeBCRDetailModal() {
		this.bcrDetailsModal = false;
		this.showBCRConflictsTab = false;
	}

	onPageChangeBCRDetail(event: PageChange) {
		this.currentPageForBCRDetail = event.pageIndex;
		this.pageSizeForBCRDetail = event.pageSize;
		this.visibleBCRDetailList = this.getPageBCRDetail(this.currentPageForBCRDetail, this.pageSizeForBCRDetail);
		this.cdRef.markForCheck();
	}

	private calculatePageStartForBCRDetail(page: number, size: number): number {
		return (page - 1) * size;
	}

	private calculatePageEndForBCRDetail(page: number, size: number): number {
		return page === 0 ? size : page * size;
	}

	private getPageBCRDetail(page: number, size: number) {
		const start = this.calculatePageStartForBCRDetail(page, size);
		const end = this.calculatePageEndForBCRDetail(page, size);
		return this.bcrDetailList.slice(start, end);
	}
	
	updateBaseProjectBCRCheck(){
		//bcr conflicts check work//
		const baseProjectProductGroup = this.buildProductGroup();
		const productGroupIds = baseProjectProductGroup.map((item: any) => parseInt(item.productGroupId));
		this.bcrDetailList = [];
		this.visibleBCRDetailList = [];
		this.checkBCRConflicts(productGroupIds, baseProjectProductGroup,"Update");
		if (!this.bcrDetailList || this.bcrDetailList.length === 0) {
			return;
		}
		//bcr conflicts check work ends //
	}

	updateBaseProject() {
		const baseProjectProductGroup : any= [];
		const baseProjectPredecessor: any = [];
		//const baseProjectProductGroup: any = [];
		this.addBaseProjectFormGroup.get('baseProjectPredecessor')?.value.forEach((item: any) => {
			baseProjectPredecessor.push({ predecessorId: item });
		});
		this.addBaseProjectFormGroup.get('baseProjectProductGroup')?.value.forEach((item: any) => {
			baseProjectProductGroup.push({ productGroupId: parseInt(item) });
		});
		const baseProjectPanelValue = this.addBaseProjectFormGroup.get('baseProjectPanel')?.value;
		const baseProjectPurposeValue = this.addBaseProjectFormGroup.get('baseProjectPurpose')?.value;
		const isRelevantForReportingEnabled = baseProjectPanelValue == 3 ? false : baseProjectPurposeValue == 6? true:false;
		let deletedBp;
		const restoring = this.isDeleted && this.editBaseProject && this.userRole?.name === 'Master'; // You are restoring

	if (restoring) {
		deletedBp = false;
	}
		this.putApiService.updateBaseProject(
			this.baseProjectID,
			this.addBaseProjectFormGroup.get('combinedName')?.value,
			baseProjectProductGroup,
			baseProjectPredecessor,
			this.addBaseProjectFormGroup.get('baseProjectDataType')?.value,
			this.addBaseProjectFormGroup.get('baseProjectPurpose')?.value,
			isRelevantForReportingEnabled,
			deletedBp
		)
			.subscribe({
				next: () => {
					this.baseProjectTypeOptions.forEach((item: any) => {
						item.disabled = true;
					});
					this.disableField = true;
					this.unsavedChanges = false;
					const title = 'Base Project Updated';
					this.notifyWidget(title, 'success');
					this.loadTableData = false;
					this.initialbaseProjectProductGroup = baseProjectProductGroup;
					if (restoring) {
				setTimeout(() => {
					this.router.navigate(['/base-projects/list']);
				}, 1000);
			}
		},						
				error: (error) => {
					let title: string;
					if (error.status == 400 || error.status == 401 || error.status == 404 || error.status == 500 || error.status == 502 || error.status == 503 || error.status == 504) {
						if (error.error.message.includes('product groups')) {
							this.pgEditErrorMessage=error.error;
							this.pgEditErrorMessage.issues.forEach(x=>{
								if(x.productGroupDesc==null){
									this.productGroupData.forEach((productGroup: any) => {
										if (parseInt(productGroup.value) == x.productGroupId) {
											x.productGroupDesc = productGroup.label.split('(')[0].trim();
										}
									});
								}
							})
							this.openpgEditValidationModal();
						}
						title = 'Error: ' + error.status + '. Unable to update Base Project. Please contact administrator.';
					}
					else {
						title = error?.message == null ? error?.error : error?.message;
					}
					if (!(error.status == 400 && error.error.message.includes('product groups'))){
						this.notifyWidget(title, 'error');
					}
					this.loadTableData = false;
				},
				complete: () => {
					this.getApiService.getBaseProjectsByBaseProjectId(this.baseProjectID).subscribe({
						next: (result:any) => {
							this.data = result;
							this.getQCPeriodByQCProjectID(result.qcProjects.id);
						}
					});
					
				// 	this.loadBaseProjectDataByID();
				// 	this.addBaseProjectFormGroup.controls.baseProjectProductGroup.setValue(baseProjectProductGroup);
				// 
				}
			});
	}

	calculateRefreshedDateTime() {
		if (this.data.updatedWhen) {
			const date = new Date(this.data.updatedWhen.toString());
			const cetTime = moment(date).tz(this.timezone).format('YYYY-MM-DD HH:mm');
			const timeZoneAbbreviation = this.getOffsetAtTime(this.timezone, this.data.updatedWhen);
			this.lastUpdateOn = "Changed by " + this.data.updatedBy + " on: " + cetTime + " " + timeZoneAbbreviation;
			this.updatedBy = this.data.lastUpdateBy;
		}
		if (this.data.dateOfCreation) {
			const date = new Date(this.data.dateOfCreation.toString());
			const cetTime = moment(date).tz(this.timezone).format('YYYY-MM-DD HH:mm');
			const timeZoneAbbreviation = this.getOffsetAtTime(this.timezone, this.data.dateOfCreation);
			this.createdOn = "Created by " + this.data.createdBy + " on: " + cetTime + " " + timeZoneAbbreviation;
			this.createdBy = this.data.lastUpdateBy;
		}
	}

	openRestoreConfirmationModal() {
		this.restoreConfirmationModal = true;
	}

	openDeleteConfirmationModal() {
		this.deleteConfirmationModal = true;
	}

	deleteBaseProject(confirmDeletion?: boolean): void {
		if (!confirmDeletion) {
			this.deleteConfirmationModal = false;
		}
		else {
			this.resetDeleteState();
			this.deleteApiService.deleteSelectedBaseProjects({ ids: [this.baseProjectID] }).subscribe({
				next: (result: any) => this.handleBPDeleteResponse(result),
				error: (error) => this.handleBPDeleteError(error)
			});
		}
	}

	resetDeleteState(): void {
		this.deletedBps = [];
		this.displayNonDeletedMessage = false;
		this.validationMessage = '';
		this.displayDeleteWithErrorMessage = false;
		this.deleteConfirmationModal = false;
	}

	handleBPDeleteResponse(result: any): void {
		if (this.allDeleted(result)) {
			this.notifySuccess();
			setTimeout(() => {
				this.router.navigate(['/base-projects/list/']);
			}, 1000);
		}
		else {
			this.handleDeleteConflicts(result);
		}
	}

	allDeleted(result: any): boolean {
		return result.every((response: any) => response.statusCode === 200);
	}

	notifySuccess(): void {
		const title = 'Base project Deleted!';
		const message = 'BP ID : ' + this.baseProjectID;
		this.notifyWidget(title, 'success', message);
	}

	handleDeleteConflicts(result: any): void {
		if (this.allNonDeleted(result)) {
			this.displayNonDeletedMessage = true;
			this.bpDeleteConflictDetails = result;
		}
		else {
			this.processPartialDelete(result);
		}
		this.openDeleteValidationModal();
	}

	allNonDeleted(result: any): boolean {
		return result.every((response: any) => response.statusCode === 400);
	}

	processPartialDelete(result: any): void {
		this.displayDeleteWithErrorMessage = true;
		this.bpDeleteConflictDetails = [];
		this.deletedBps = [];
		result.forEach((item: any) => {
			if (item.statusCode === 200) {
				this.deletedBps.push(item.baseProjectId);
			} else {
				this.bpDeleteConflictDetails.push(item);
			}
		});
	}

	handleBPDeleteError(error: any): void {
		const title = this.getBPDeleteErrorMessage(error);
		this.notifyWidget(title, 'error');
	}

	getBPDeleteErrorMessage(error: any): string {
		if ([400, 401, 404, 500, 502, 503, 504].includes(error.status)) {
			return 'Error: ' + error.status + '. Unable to delete base project. Please contact administrator.';
		}
		return error?.message == null ? error?.error : error?.message;
	}

	openDeleteValidationModal() {
		this.deleteValidationModal = true;
	}

	closeDeleteValidationModal() {
		this.deleteValidationModal = false;
	}

	openBPValidationModal() {
		this.bpValidationModal = true;
	}

	closeBPValidationModal(event: any): void {
		if (event) {
			this.bpValidationModal = false;
		}
	}

	displayResetFormModal(){
		this.cancelFormChangesModal = true;
	}

	resetBPForm(discardChanges?: boolean): void{
		if (!discardChanges) {
			this.cancelFormChangesModal = false;
		}
		else {
			this.showError=false;
			if(this.baseProjectID){
				// Edit base or copy scenario - reset to original values
				this.patchBaseProjectForm(this.data);
				const selectedPredecessors: any = [];
				this.data.predecessors.forEach((item: any) => { selectedPredecessors.push(item.predecessorId.toString()) });
				this.addBaseProjectFormGroup.controls.baseProjectPredecessor.setValue(selectedPredecessors);
				const selectedProductGroups: any = [];
				this.data.productGroups.forEach((item: any) => { selectedProductGroups.push(item.productGroupId.toString()) });
				this.addBaseProjectFormGroup.controls.baseProjectProductGroup.setValue(selectedProductGroups);
				this.splitBaseProjectName(this.data.name, this.data.id);
				this.disableAutoGenerateName = false;
				this.firsttime = true;

				// Reset purpose to original value for both edit and copy scenarios
				this.addBaseProjectFormGroup.controls.baseProjectPurpose.setValue(this.data.purposeId);

				if(this.copyBaseProject){
					const panelOptions = this.baseProjectPanelOptions;
					this.baseProjectPanelOptions = [];
					const typeOptions = this.baseProjectTypeOptions;
					this.baseProjectTypeOptions = [];
					setTimeout(() => {
						panelOptions.forEach(item => {
							item.checked = item.value == this.data.panelId;
						});
						this.baseProjectPanelOptions = panelOptions;
						typeOptions.forEach(item => {
							item.checked = item.value == this.data.typeId;
						});
						this.addBaseProjectFormGroup.controls.baseProjectPeriodicity.setValue(this.data.periodicityId);
						// Ensure purpose is set to original value for copy scenario as well
						this.addBaseProjectFormGroup.controls.baseProjectPurpose.setValue(this.data.purposeId);
						this.baseProjectTypeOptions = typeOptions;
						this.hideFieldsForDistributor = false;
					}, 100);
				}
			}
			else{
				// Create scenario - auto-select Production (value '1')
				this.addBaseProjectFormInitializer();
				this.initializeValueChanges();
				const panelOptions = this.baseProjectPanelOptions;
				this.baseProjectPanelOptions = [];
				const typeOptions = this.baseProjectTypeOptions;
				this.baseProjectTypeOptions = [];
				setTimeout(() => {
					panelOptions.forEach(item => {
						item.checked = false;
					});
					this.baseProjectPanelOptions = panelOptions;
					typeOptions.forEach(item => {
						item.checked = false;
					});
					this.baseProjectTypeOptions = typeOptions;
					this.hideFieldsForDistributor = false;
					// For create scenario, set purpose to Production ('1')
					this.addBaseProjectFormGroup.controls.baseProjectPurpose.setValue('1');
				}, 100);
			}
			this.cancelFormChangesModal = false;
		}
	}

	//QC Project Settings
	qcProjectSettingsFormInitializer() {
		this.qcProjectSettingsFormGroup = this.formBuilder.group({
			sqcMode: '0',
			resetCorrectionTypeId: null,
		});
	}

	getResetCorrectTypeList() {
		this.resetDataCorrectionType$ = this.getApiService.getAsyncResetCorrectionType()
			.pipe(
				map((resetCorrectionTypeList: any) => resetCorrectionTypeList.map((resetCorrectionType: any) => ({ label: resetCorrectionType.name, value: (resetCorrectionType.id).toString(), qcProject: resetCorrectionType.qcProject } as AutocompleteOption)))
			);
		this.resetDataCorrectionType$.subscribe({
			next: (result) => {
				this.resetDataCorrectionTypeList = result;
			}
		});
	}

	updateQCProjectSettings() {
		this.loadTableData = true;
		this.putApiService.updateQCProject(
			this.qcProjectID,
			this.qcProjectSettingsFormGroup.get('sqcMode')?.value,
			this.isAutomatedPriceCheck,
			this.isAutoLoad,
			this.qcProjectSettingsFormGroup.get('resetCorrectionTypeId')?.value,
		)
			.subscribe({
				next: () => {
					const title = 'QC Project Updated';
					this.unsavedChanges = false;
					this.notifyWidget(title, 'success');
					this.loadTableData = false;
				},
				error: (error) => {
					let title: string;
					let message: string;
					if (error.status == 400 || error.status == 401 || error.status == 404 || error.status == 500 || error.status == 502 || error.status == 503 || error.status == 504) {
						title = 'Error: ' + error.status + '. QC Settings update failed. Please contact administrator.';
						this.notifyWidget(title, 'error');
					}
					else if (error.status == 403) {
						UserService.forbiddenError = true;
						title = 'Operation Restricted';
						message = 'Insufficient permission for selected BP Country';
						this.notifyWidget(title, 'error', message);
					}
					else {
						title = error?.message == null ? error?.error : error?.message;
						this.notifyWidget(title, 'error');
					}
					this.loadTableData = false;
				}
			});
	}

	qcProjectSettingInput(event: any, fieldName: string) {
		if (fieldName == 'isAutomatedPriceCheck') {
			this.isAutomatedPriceCheck = event.detail;
		}
		else {
			this.isAutoLoad = event.detail;
		}
	}

	resetQCForm(discardChanges?: boolean): void{
		if (!discardChanges) {
			this.cancelFormChangesModal = false;
		}
		else {
			const sqcModeOptions = this.sqcModeOptions;
			this.sqcModeOptions = [];
			setTimeout(() => {
				this.qcProjectSettingsFormGroup.patchValue({
					sqcMode: this.data.qcProjects.sqcMode,
					resetCorrectionTypeId: this.data.qcProjects.resetCorrectionTypeId ? this.data.qcProjects.resetCorrectionTypeId.toString() : null,
				});
				this.isAutomatedPriceCheck = this.data.qcProjects.isAutomatedPriceCheck;
				this.isAutoLoad = this.data.qcProjects.isAutoLoad;
				sqcModeOptions.forEach(item => {
					if(item.value == this.data.qcProjects.sqcMode.sqcMode){
						item.checked = false;
					}
				});
				this.sqcModeOptions = sqcModeOptions;
			}, 100);
			this.cancelFormChangesModal = false;
		}
	}
	//QC Project Settings

	//QC Period		  
	async getQCPeriodByQCProjectID(qcProjectID: number) {
		try {
		  this.loadQCPeriodTableData = true;
	  
		  const result: any = await firstValueFrom(this.getApiService.getQCPeriodByQCProjectID(qcProjectID));
		  const allRefProjectIds = new Set<number>();
		  const refProjectUsageMap: Record<string, { refPeriodId: number, index: number }> = {};
	  
		  // Step 1: Extract all reference project IDs from all periods
		  for (const item of result.qcPeriods) {
			for (const itemPeriod of item.periods || []) {
			  const refKey = `${item.id}_${itemPeriod.index}`;
			  refProjectUsageMap[refKey] = {
				refPeriodId: itemPeriod.refPeriodId,
				index: itemPeriod.index
			  };
			  allRefProjectIds.add(itemPeriod.refProjectId);
			}
		  }
	  
		  // Step 2: Fetch all base project details
		  for (const refProjectId of allRefProjectIds) {
			if (!this.projectDetailsCache[refProjectId]) {
			  const details = this.data;
			  this.projectDetailsCache[refProjectId] = details;
			  if (details?.periodicityId !== undefined) {
				this.projectPeriodicityMap[refProjectId] = details.periodicityId;
			  } else {
				console.warn(`⚠️ Periodicity ID missing for project ${refProjectId}`, details);
			  }		
			}
		  }
	  
		  // Step 3: Fetch all unique periodicities
		  const periodicityIds = new Set<number>(
			Array.from(allRefProjectIds)
			  .map(refId => this.projectPeriodicityMap[refId])
			  .filter(Boolean)
		  );
	  
		for (const periodicityId of periodicityIds) {
			if (!this.periodsByPeriodicity[periodicityId]) {
			  const periods = await firstValueFrom(
				this.getApiService.getAsyncPeriodsByPeriodicity(periodicityId, 1000) as Observable<any[]>
			  ).then(response =>
				response.map((p: any) => ({
				  label: `(${p.id}) - ${p.name}`,
				  value: p.id.toString()
				}))
			  ).catch((err) => {
				console.error(`❌ Failed loading periods for periodicity ${periodicityId}`, err);
				return [];
			  });
	  
			  this.periodsByPeriodicity[periodicityId] = periods;
			  console.log('📦 Appended periods for periodicity:', periodicityId);
			}
		  }
	  
		  // Step 4: Resolve QC Periods and labels
		  for (const item of result.qcPeriods) {
			const match = this.periodList.find(p => p.value === item.periodId?.toString());
			item.periodName = match?.label ?? `(${item.periodId}) - N/A`;
			
	  
			for (const itemPeriod of item.periods || []) {
			  const refProjectId = itemPeriod.refProjectId;
			  const refPeriodId = itemPeriod.refPeriodId;
			  const index = itemPeriod.index;
			  const periodicityId = this.projectPeriodicityMap[refProjectId];
			  const periodsList = this.periodsByPeriodicity[periodicityId] || [];
	  
			  const found = periodsList.find(p => p.value == refPeriodId.toString());
			  item[`refPeriod${index + 1}`] = found ? found.label : 'N/A';
	  
			}
	  
			item.status = this.setStatusValue(item);
		  }
	  
		  this.qcPeriodData = result.qcPeriods;
		  this.visibleQCPeriods = this.getPageQCPeriods(this.currentPage, this.pageSize);
	  
		  if (this.allRows()?.some(row => row.isSelected)) {
			this.clearSelection();
		  }
	  
		  this.loadQCPeriodTableData = false;
		  GetApiService.loadQCPeriodTableData = false;
		  this.showQCPeriodTable = true;
		  this.cdRef.markForCheck();
	  
		} catch (error: any) {
		  this.loadQCPeriodTableData = false;
		  if (error.status === 404) {
			this.visibleQCPeriods = [];
			this.qcPeriodData = null;
			this.showQCPeriodTable = false;
		  } else {
			const title = `Error: ${error.status}. Unable to fetch QC Periods. Please contact administrator.`;
			this.notifyWidget(title, 'error');
		  }
		}
	  }
	  

	  async getCachedPeriodsByPeriodicity(periodicityId: number): Promise<any[]> {
		if (this.periodsByPeriodicity[periodicityId]) {
		  return this.periodsByPeriodicity[periodicityId];
		}
	  
		try {
			const periods = await firstValueFrom(
				(this.getApiService.getAsyncPeriodsByPeriodicity(periodicityId, 1000) as Observable<any[]>).pipe(
				  map((response: any[]) =>
					response.map((p: any) => ({
					  label: `(${p.id}) - ${p.name}`,
					  value: p.id.toString()
					}))
				  )
				)
			  );
												
			  this.periodsByPeriodicity[periodicityId] = periods;
			  
		  return periods;
		} catch (err) {
		  console.error(`❌ Failed to fetch periods for periodicity ${periodicityId}`, err);
		  this.periodsByPeriodicity[periodicityId] = [];
		  return [];
		}
	  }
	  

	  
	  async getCachedProjectDetails(refProjectId: number): Promise<any> {
		if (this.projectDetailsCache[refProjectId]) {
		  return this.projectDetailsCache[refProjectId];
		}
	  
		const projectDetails = await firstValueFrom(
		  this.getApiService.getBaseProjectsByBaseProjectId(refProjectId)
		);
	  
		this.projectDetailsCache[refProjectId] = projectDetails;
		return projectDetails;
	  }
	  
	  
	  
  
	setStatusValue(item: any) {
		let status;
		if (item.status == 0) {
			status = 'N/A';
		}
		else if (item.status == 6) {
			status = 'QC';
		}
		else {
			status = null;
		}
		return status;
	}

	onPageChangeQCPeriods(event: PageChange) {
		this.currentPage = event.pageIndex;
		this.pageSize = event.pageSize;
		this.visibleQCPeriods = this.getPageQCPeriods(this.currentPage, this.pageSize);
		this.cdRef.markForCheck();
	}

	private calculatePageStart(page: number, size: number): number {
		return (page - 1) * size;
	}

	private calculatePageEnd(page: number, size: number): number {
		return page === 0 ? size : page * size;
	}

	private getPageQCPeriods(page: number, size: number) {
		const start = this.calculatePageStart(page, size);
		const end = this.calculatePageEnd(page, size);
		return this.qcPeriodData.slice(start, end);
	}

	getQCPeriodByQCPeriodId(qcPeriodId: number) {
		this.loadQCPeriodTableData = true;
		this.disableQCPeriodEdit = false;
		this.qcPeriodID = qcPeriodId;
		this.getApiService.getQCPeriodByQCPeriodId(this.qcPeriodID).subscribe({
			next: (result: any) => {
				this.qcPeriodFormInitializer(result, 'edit');
				this.qcPeriodFormModal = true;
				this.qcPeriodFormGroup.get('periodId')?.disable();
				setTimeout(() => {
					if (!this.userRole) {
						this.disableQCPeriodEdit = true;
					}
				}, 100);
				this.calculateQCPeriodDateTime(result);
				this.loadQCPeriodTableData = false;
			},
			error: (error) => {
				let title: string;
				if (error.status == 400 || error.status == 401 || error.status == 500 || error.status == 502 || error.status == 503 || error.status == 504) {
					title = 'Error: ' + error.status + '. Unable to fetch QC Period. Please contact administrator.';
				}
				else if (error.status == 404) {
					title = 'No QC Period exists with Id ' + this.qcPeriodID;
				}
				else {
					title = error?.message == null ? error?.error : error?.message;
				}
				this.notifyWidget(title, 'error');
				this.loadQCPeriodTableData = false;
			},
		});
	}

	calculateQCPeriodDateTime(qcPeriodData: any) {
		if (qcPeriodData.updatedWhen) {
			const date = new Date(qcPeriodData.updatedWhen.toString());
			const cetTime = moment(date).tz(this.timezone).format('YYYY-MM-DD HH:mm');
			const timeZoneAbbreviation = this.getOffsetAtTime(this.timezone, qcPeriodData.updatedWhen);
			this.qcPeriodLastUpdateOn = "Changed by: " + qcPeriodData.updatedBy + " on: " + cetTime + " " + timeZoneAbbreviation;
			this.qcPeriodUpdatedBy = this.data.lastUpdateBy;
		}
		else if (qcPeriodData.dateOfCreation) {
			this.qcPeriodLastUpdateOn = '';
			const date = new Date(qcPeriodData.dateOfCreation.toString());
			const cetTime = moment(date).tz(this.timezone).format('YYYY-MM-DD HH:mm');
			const timeZoneAbbreviation = this.getOffsetAtTime(this.timezone, qcPeriodData.dateOfCreation);
			this.qcPeriodCreatedOn = "Created by: " + qcPeriodData.createdBy + " on: " + cetTime + " " + timeZoneAbbreviation;
			this.qcPeriodCreatedBy = this.data.lastUpdateBy;
		}
	}

	openDeleteQCPeriodConfirmationModal() {
		this.deleteQCPeriodConfirmationModal = true;
	}

	getSelectedQCPeriodNames(): string[] {
		return this.selectedEntities.map((item: any) => item.periodName);
	}

	deleteQCPeriod(confirmDeletion?: boolean): void {
		if (!confirmDeletion) {
			this.deleteQCPeriodConfirmationModal = false;
		}
		else if (this.isDeletionNotAllowed()) {
			this.showQCPeriodDeletionError();
			this.deleteQCPeriodConfirmationModal = false;
		}
		else {
			this.loadQCPeriodTableData = true;
			this.initiateDeletion();
		}
	}

	isDeletionNotAllowed(): boolean {
		return this.qcPeriodData.length <= 1 || this.qcPeriodData.length === this.selectedEntities.length;
	}

	showQCPeriodDeletionError(): void {
		const title = 'All QC Periods can not be Deleted';
		const message = 'At least one QC-Period must exist. Deletion of all QC-Periods is not allowed.';
		this.notifyWidget(title, 'error', message);
	}

	initiateDeletion(): void {
		const selectedQCPeriodIds: number[] = this.selectedEntities.map((item: any) => item.id);
		const selectedQCPeriodNames: string[] = this.getSelectedQCPeriodNames();
		this.deleteQCPeriodConfirmationModal = false;
		this.deleteApiService.deleteSelectedQCPeriods({ ids: selectedQCPeriodIds }).subscribe({
			next: (response) => this.handleQCPeriodDeletionSuccess(selectedQCPeriodNames, response),
			error: (error) => this.handleQCPeriodDeletionError(error),

		});
	}

	handleQCPeriodDeletionSuccess(selectedQCPeriodNames: string[], deleteQCPeriodResponse: any): void {
		if(deleteQCPeriodResponse.length === 1){
			const title = 'QC Period Deleted';
			const message = selectedQCPeriodNames[0] ? selectedQCPeriodNames[0] : '';
			this.notifyWidget(title, 'success', message);
		}
		else{
			const deletedArray: any = [];
			const conflictArray: any = [];
			deleteQCPeriodResponse.forEach(item => {
				if (item.statusCode === 200) {
					deletedArray.push(item);
				} 
				else if (item.statusCode === 400) {
					conflictArray.push(item);
				}
			});
			const title = `${deletedArray.length} QC Periods Deleted`;
			this.notifyWidget(title, 'success');
			if (conflictArray.length > 0) {
				this.openQCDeleteValidationModal();
				conflictArray.forEach((conflictedItem: any) => {
					this.selectedEntities.forEach((selectedItem: any) => {
						if(selectedItem.id == conflictedItem.qcPeriodId){
							this.QCPeriodQCStatusIds.push(selectedItem.periodId);
						}
					})
				})
			}
		}
		// if(this.allRows()?.filter((row) => row.isSelected).length){
			this.clearSelection();
		// }
		setTimeout(() => {
			this.handleDeleteCompleteResponse();
		}, 500);
	}

	handleQCPeriodDeletionError(error: any): void {
		this.QCPeriodQCStatusIds = [];
		let title: string;
		let message: string;
		if (this.isQCPeriodError(error.status)) {
			title = `Error: ${error.status}. Unable to delete QC Period. Please contact administrator.`;
			if (error.error.message.includes('QC Status')) {
				this.openQCDeleteValidationModal();
			}
			else {
				this.notifyWidget(title, 'error');
			}
		} else if (error.status === 403) {
			UserService.forbiddenError = true;
			title = 'Operation Restricted';
			message = 'Insufficient permission for selected BP Country';
			this.notifyWidget(title, 'error', message);
		} else {
			title = error?.message ?? error?.error;
			this.notifyWidget(title, 'error');
		}
		this.loadQCPeriodTableData = false;
	}

	handleDeleteCompleteResponse(){
		this.getQCPeriodByQCProjectID(this.qcProjectID);
	}

	isQCPeriodError(status: number): boolean {
		return [400, 401, 404, 500, 502, 503, 504].includes(status);
	}


  openQCPeriodFormModal() {
    if (this.qcperiodError) {
        this.qcPeriodFormModal = true;
    } else {
        this.qcPeriodID = null;
        this.qcPeriodFormInitializer('', 'add');
    }
	this.qcperiodError = false;
	}

	qcPeriodFormInitializer(qcPeriodData?: any, formType?: string) {
		this.qcPeriodFormGroup = this.formBuilder.group({
			status: qcPeriodData ? qcPeriodData.status : 0,
			periodId: qcPeriodData ? qcPeriodData.periodId : this.currentPeriodList[0].value,
			periods: this.formBuilder.array([]),
			stockBaseProjectId: this.getStockBaseProjectId(qcPeriodData),
			stockPeriodId: this.getStockPeriodId(qcPeriodData),
		});
		this.addPeriods((formType === 'add' ? this.currentPeriodList : qcPeriodData?.periods), formType);
		setTimeout(() => {
			this.periods.controls.forEach((control, index) => {
				const refProjectId = control.get('refProjectId')?.value;
				if (refProjectId) {
				  this.onReferenceProjectSelected(refProjectId, index);
				}
			  });
			});
		this.qcPeriodFormModal = true;
	}

	getStockBaseProjectId(qcPeriodData: any) {
		return (qcPeriodData && qcPeriodData?.stockInitialization) ? qcPeriodData?.stockInitialization?.stockBaseProjectId : null;
	}

	getStockPeriodId(qcPeriodData: any) {
		return (qcPeriodData && qcPeriodData?.stockInitialization) ? qcPeriodData?.stockInitialization?.stockPeriodId : null;
	}

	updateReferencePeriodsFormValue() {
		const selectedPeriodValue: any = this.qcPeriodFormGroup.get('periodId')?.value;
		const currentIndex = this.currentPeriodList.findIndex(period => period.value === selectedPeriodValue);
		if (currentIndex > 0) {
			this.setRefPeriodId(0, this.currentPeriodList[currentIndex + 1].value);
		}
		const year = parseInt(selectedPeriodValue.toString().substring(0, 4));
		const lastYear = year - 1;

		if (selectedPeriodValue.toString().includes('week')) {
			this.updateWeekReferencePeriod(selectedPeriodValue, lastYear);
		}
		else {
			this.updateDateReferencePeriod(selectedPeriodValue, year, lastYear);
		}
	}

	onReferenceProjectSelected(projectId: string, index: number) {
		if (!projectId) return;  
	
		this.getApiService.getBaseProjectsByBaseProjectId(+projectId).subscribe({
			next: (project: any) => {
				const periodicityId = project?.periodicityId;
				if (!periodicityId) {
					this.refPeriodLists[index] = [];  
					return;
				}
	
				this.getApiService.getAsyncPeriodsByPeriodicity(periodicityId, 1000)
					.pipe(
						map((response: any) => {
							const list = response as any[];
							return list.map((p: any) => ({
								label: `(${p.id}) - ${p.name}`,
								value: p.id.toString(),
							}));
						})
					)
					.subscribe({
						next: (periodList: AutocompleteOption[]) => {
							this.refPeriodLists[index] = periodList;
						},
						error: () => {
							this.refPeriodLists[index] = []; 
						}
					});
			},
			error: () => {
				this.refPeriodLists[index] = []; 
			}
		});
	}
	
	
	  
	setRefPeriodId(index: number, value: any) {
		(<FormArray>this.qcPeriodFormGroup.get('periods')).at(index).get('refPeriodId')?.setValue(value);
	}

	updateWeekReferencePeriod(selectedPeriodValue: any, lastYear: number) {
		const week = selectedPeriodValue.match(/week:(\d{2})/)[1];
		const lastYearWeekValue = `${lastYear}/week:${week}`;
		const lastYearPeriod = this.currentPeriodList.find(period => period.value.startsWith(lastYearWeekValue));
		this.setRefPeriodId(6, lastYearPeriod ? lastYearPeriod.value : null);
	}

	updateDateReferencePeriod(selectedPeriodValue: string, year: number, lastYear: number) {
		const lastYearDateValue = selectedPeriodValue.toString().replace(year.toString(), lastYear.toString());
		const lastYearPeriod = this.currentPeriodList.find(period => period.value === lastYearDateValue);
		this.setRefPeriodId(6, lastYearPeriod ? lastYearPeriod.value : null);
	}


	get periods(): FormArray {
		return this.qcPeriodFormGroup.get('periods') as FormArray;
	}

	addPeriods(qcPeriodData: any, formType?: string) {
		const qcPeriodIndex: any = [];
		qcPeriodData.forEach((item: any) => {
			qcPeriodIndex.push(item.index);
		});
		for (let i = 0; i < 7; i++) {
			if (i == 0) {
				this.addFirstPeriod(qcPeriodData, formType, i);
			}
			else if (i == 6) {
				this.addLastPeriod(qcPeriodData, formType, i);
			}
			else {
				this.addMiddlePeriod(qcPeriodData, formType, i, qcPeriodIndex);
			}
		}
	}

	addFirstPeriod(qcPeriodData: any, formType?: string, i?: number) {
		if (this.qcPeriodID) {
			this.addFirstPeriodForEdit(qcPeriodData, formType, i);
		}
		else {
			this.addFirstPeriodForAdd(i);
		}
	}

	addFirstPeriodForEdit(qcPeriodData: any, formType?: string, i?: number) {
		const index = qcPeriodData.findIndex(item => item.index == 0);
		let id: any;
		let refProjectIdValue: any;
		let refPeriodIdValue: any;
		if (formType == 'add') {
			id = null;
			refProjectIdValue = this.baseProjectID;
			refPeriodIdValue = this.currentPeriodList[1].value;
		}
		else {
			id = qcPeriodData[index] ? qcPeriodData[index].id : null;
			refProjectIdValue = qcPeriodData[index] ? qcPeriodData[index].refProjectId : null;
			refPeriodIdValue = qcPeriodData[index] ? qcPeriodData[index].refPeriodId : null;
		}
		if (index > -1) {
			this.periods.push(this.formBuilder.group({
				id: id,
				index: i,
				refProjectId: [refProjectIdValue, Validators.required],
				refPeriodId: [refPeriodIdValue, Validators.required],
			}));
		}
		else {
			this.periods.push(this.formBuilder.group({
				id: null,
				index: i,
				refProjectId: [null, Validators.required],
				refPeriodId: [null, Validators.required],
			}));
		}
	}

	addFirstPeriodForAdd(i?: number) {
		this.periods.push(this.formBuilder.group({
			id: null,
			index: i,
			refProjectId: [this.baseProjectID, Validators.required],
			refPeriodId: [this.currentPeriodList[1].value, Validators.required],
		}));
	}

	addLastPeriod(qcPeriodData: any, formType?: string, i?: number) {
		if (this.qcPeriodID) {
			this.addLastPeriodForEdit(qcPeriodData, formType, i);
		}
		else {
			this.addLastPeriodForAdd(formType, i)
		}
	}

	addLastPeriodForEdit(qcPeriodData: any, formType?: string, i?: number) {
		let fields!: any;
		const index = qcPeriodData.findIndex(item => item.index == 6);
		let id: any;
		if (formType == 'add') {
			id = null;
		}
		else {
			id = qcPeriodData[index] ? qcPeriodData[index].id : null;
		}
		if (index > -1) {
			fields = this.formBuilder.group({
				id: id,
				index: i,
			});
			if (qcPeriodData[index]?.refProjectId) {
				fields.addControl('refProjectId', this.formBuilder.control(qcPeriodData[index].refProjectId, [Validators.required]));
			}
			else {
				fields.addControl('refProjectId', this.formBuilder.control(this.baseProjectID));
			}
			if (qcPeriodData[index]?.refPeriodId) {
				fields.addControl('refPeriodId', this.formBuilder.control(qcPeriodData[index].refPeriodId, [Validators.required]));
			}
			else {
				fields.addControl('refPeriodId', this.formBuilder.control(this.getReferencePeriodForLastYear(this.data.periodicityId, this.periodList)));
			}
			this.periods.push(fields);
		}
		else {
			this.periods.push(this.formBuilder.group({
				id: null,
				index: i,
				refProjectId: null,
				refPeriodId: null
			}));
		}
	}

	addLastPeriodForAdd(formType?: string, i?: number) {
		this.periods.push(this.formBuilder.group({
			id: null,
			index: i,
			refProjectId: [formType == 'add' ? this.baseProjectID : null, Validators.required],
			refPeriodId: [formType == 'add' ? this.getReferencePeriodForLastYear(this.data.periodicityId, this.currentPeriodList) : null, Validators.required],
		}));
	}

	addMiddlePeriod(qcPeriodData: any, formType?: string, i?: number, qcPeriodIndex?: any) {
		if (this.qcPeriodID) {
			this.addMiddlePeriodForEdit(qcPeriodData, formType, i, qcPeriodIndex);
		}
		else {
			this.addMiddlePeriodForAdd(i)
		}
	}

	addMiddlePeriodForEdit(qcPeriodData: any, formType?: string, i?: number, qcPeriodIndex?: any) {
		if (qcPeriodIndex.includes(i)) {
			const index = qcPeriodData.findIndex(item => item.index == i);
			let id: any;
			if (formType == 'add') {
				id = null;
			}
			else {
				id = qcPeriodData[index] ? qcPeriodData[index].id : null;
			}
			if (index > -1) {
				this.handleMiddlePeriodFormFields(id, i, qcPeriodData, index)
			}
		}
		else {
			this.addNewPeriod(i);
		}
	}

	handleMiddlePeriodFormFields(id: any, i?: number, qcPeriodData?: any, index?: any) {
		const fields: any = this.formBuilder.group({
			id: id,
			index: i,
		});
		if (qcPeriodData[index]?.refProjectId) {
			fields.addControl('refProjectId', this.formBuilder.control(qcPeriodData[index].refProjectId, [Validators.required]));
		}
		else {
			fields.addControl('refProjectId', this.formBuilder.control(null));
		}
		if (qcPeriodData[index]?.refPeriodId) {
			fields.addControl('refPeriodId', this.formBuilder.control(qcPeriodData[index].refPeriodId, [Validators.required]));
		}
		else {
			fields.addControl('refPeriodId', this.formBuilder.control(null));
		}
		this.periods.push(fields);
	}

	addNewPeriod(i?: number) {
		this.periods.push(this.formBuilder.group({
			id: null,
			index: i,
			refProjectId: null,
			refPeriodId: null
		}));
	}

	addMiddlePeriodForAdd(i?: number) {
		this.addNewPeriod(i);
	}

	validateQCForm(formControlName: any, index?: any, period?: any) {
		if (index && period) {
			this.validateReferenceForm(formControlName, index, period);
		}
		else {
			this.validateStockForm(formControlName);
		}
	}

	validateReferenceForm(formControlName: any, index: number, period: any) {
		if (period.value.id || index == 0) {
			return;
		}
		else {
			const formControl = ((this.qcPeriodFormGroup.get('periods') as FormArray).controls);
			if (formControlName == 'referenceProject') {
				if (formControl[index].get('refProjectId')?.value) {
					formControl[index].get('refPeriodId')?.setValidators([Validators.required]);
				}
				else {
					formControl[index].get('refPeriodId')?.clearValidators();
				}
			}
			else {
				if (formControl[index].get('refPeriodId')?.value) {
					formControl[index].get('refProjectId')?.setValidators([Validators.required]);
				}
				else {
					formControl[index].get('refProjectId')?.clearValidators();
				}
			}
			formControl[index].get('refProjectId')?.updateValueAndValidity();
			formControl[index].get('refPeriodId')?.updateValueAndValidity();
		}
	}

	validateStockForm(formControlName: any) {
		if (formControlName == 'stockProject') {
			if (this.qcPeriodFormGroup.get('stockBaseProjectId')?.value) {
				this.qcPeriodFormGroup.get('stockPeriodId')?.setValidators([Validators.required]);
			}
			else {
				this.qcPeriodFormGroup.get('stockPeriodId')?.clearValidators();
			}
		}
		else {
			if (this.qcPeriodFormGroup.get('stockPeriodId')?.value) {
				this.qcPeriodFormGroup.get('stockBaseProjectId')?.setValidators([Validators.required]);
			}
			else {
				this.qcPeriodFormGroup.get('stockBaseProjectId')?.clearValidators();
			}

		}
		this.qcPeriodFormGroup.get('stockBaseProjectId')?.updateValueAndValidity();
		this.qcPeriodFormGroup.get('stockPeriodId')?.updateValueAndValidity();
	}

	updateReferencePeriods(event: any) {
		const selectedPeriodValue = event.detail;
		const currentIndex = this.currentPeriodList.findIndex(period => period.value === selectedPeriodValue);

		if (currentIndex > 0) {
			(<FormArray>this.qcPeriodFormGroup.get('periods')).at(0).get('refPeriodId')?.setValue(this.currentPeriodList[currentIndex + 1].value);
		} else {
			(<FormArray>this.qcPeriodFormGroup.get('periods')).at(0).get('refPeriodId')?.setValue(null);
		}
		const year = parseInt(selectedPeriodValue.substring(0, 4));
		const lastYear = year - 1;

		if (selectedPeriodValue.includes('week')) {
			const week = selectedPeriodValue.match(/week:(\d{2})/)[1];
			const lastYearWeekValue = `${lastYear}/week:${week}`;
			const lastYearPeriod = this.currentPeriodList.find(period => period.value.startsWith(lastYearWeekValue));
			if (lastYearPeriod) {
				(<FormArray>this.qcPeriodFormGroup.get('periods')).at(6).get('refPeriodId')?.setValue(lastYearPeriod.value);
			}
			else {
				(<FormArray>this.qcPeriodFormGroup.get('periods')).at(6).get('refPeriodId')?.setValue(null);
			}
		}
		else {
			const lastYearDateValue = selectedPeriodValue.replace(year.toString(), lastYear.toString());
			const lastYearPeriod = this.currentPeriodList.find(period => period.value === lastYearDateValue);

			if (lastYearPeriod) {
				(<FormArray>this.qcPeriodFormGroup.get('periods')).at(6).get('refPeriodId')?.setValue(lastYearPeriod.value);
			}
			else {
				(<FormArray>this.qcPeriodFormGroup.get('periods')).at(6).get('refPeriodId')?.setValue(null);
			}
		}
	}

	getReferencePeriodForLastYear(periodicityId: number, periodicityList: any[]): any {
		const periodicityMap: { [key: number]: number } = {
			2: 52,
			4: 12,
			5: 6,
			6: 3,
			7: 1,
			8: 5,
			9: 5,
			16: 1,
			17: 4,
			18: 1,
			19: 2,
			21: 2,
			22: 1,
			23: 2,
			24: 1,
			25: 1,
			26: 2,
			27: 3,
			28: 1,
			29: 1,
			30: 1,
			31: 1,
			32: 1,
			33: 1,
			34: 3,
		};
		const index = periodicityMap[periodicityId];
		return index !== undefined ? periodicityList[index].value : null;
	}


	getPeriodListByPeriodicityId(periodicityId: number, limit: number) {
		this.periods$ = this.getApiService.getAsyncPeriodsByPeriodicity(periodicityId, limit)
			.pipe(
				map((periodList: any) => periodList.map((period: any) => ({ label: ('(' + period.id + ') - ' + period.name), value: (period.id).toString() } as AutocompleteOption)))
			);
		this.periods$.subscribe({
			next: (result) => {
				this.periodList = result;
				if (result.length == 1000) {
					const dateParts = this.splitPeriodName(result[999].label).split('(')[1].split(')')[0].split(' ')[2].split('.');
					const formattedDate = `${dateParts[1]}-${dateParts[0]}-${dateParts[2]}`;
					this.periodswithdate$ = this.getApiService.getAsyncPeriodsByPeriodicityandDate(periodicityId, formattedDate, limit)
						.pipe(
							map((periodList: any) => periodList.map((period: any) => ({ label: ('(' + period.id + ') - ' + period.name), value: (period.id).toString() } as AutocompleteOption)))
						);
					this.periodswithdate$.subscribe({
						next: (result) => {
							this.periodList.push(...result);
						}
					});
				}
			}
		});
	}

	splitPeriodName(inputText: any){
		return inputText.split(") -")[1]; 
	}


	getAsyncCurrentPeriodsByPeriodicity(periodicityId: number) {
		this.currentPeriods$ = this.getApiService.getAsyncCurrentPeriodsByPeriodicity(periodicityId)
			.pipe(
				map((periodList: any) => periodList.map((period: any) => ({ label: ('(' + period.id + ') - ' + period.name), value: (period.id).toString() } as AutocompleteOption)))
			);
		this.currentPeriods$.subscribe({
			next: (result) => {
				this.currentPeriodList = result;
				this.getQCPeriodByQCProjectID(this.qcProjectID);
			}
		});
	}


	getBaseProjectForQCPeriod() {
		const countryId: any = [this.data.countryId];
		const productGroups: any = [];
		this.data.productGroups.forEach((item: any) => {
			productGroups.push(item.productGroupId);
		})
		const periodicityId: any = [];
		const panelId: any = [this.data.panelId];
		const typeId: any = this.data.typeId == 3 ? [1, 2, 3] : [this.data.typeId];
		this.baseProjectForQCPeriod$ = this.postApiService.getBaseProjectsListForFilter(
			0,
			null,
			countryId,
			productGroups,
			periodicityId,
			panelId,
			typeId
		)
			.pipe(
				map((baseProjectList: any) => baseProjectList.records.map((baseProject: any) => ({ label: ('(' + baseProject.id + ') - ' + baseProject.name), value: (baseProject.id).toString() } as AutocompleteOption)))
			);
		this.baseProjectForQCPeriod$.subscribe({
			next: (result: any) => {
				this.baseProjectListForQCPeriod = result;
			},
			error: (error) => {
				let title!: string;
				if (error.status == 404) {
					title = 'No records found.';
				}
				else {
					if (
						error.status == 400 ||
						error.status == 401 ||
						error.status == 500 ||
						error.status == 502 ||
						error.status == 503 ||
						error.status == 504
					) {
						title = 'Error: ' + error.status + '. Unable to fetch Base Project. Please contact administrator.';
					}
				}
				this.notifyWidget(title, 'error');
			},
		});
	}

	saveQCPeriod(confirmsave?: boolean): void {
		if (!confirmsave) {
			this.qcPeriodFormModal = false;
		}
		else {
			this.loadQCPeriodTableData = true;
			if (this.qcPeriodID) {
				this.updateQCPeriod();
			}
			else {
				this.createQCPeriod();
			}
		}
	}

	createQCPeriod() {
		const periods = this.getValidPeriods();
		const payload = this.buildPayload(periods);
		this.postApiService.createQCPeriod(payload).subscribe({
			next: () => this.handleQCPeriodCreateSuccess(),
			error: (error) => this.handleQCPeriodCreateError(error),
			complete: () => this.completeQCPeriodCreateRequest(),
		});
	}

	getValidPeriods() {
		return this.qcPeriodFormGroup.get('periods')?.value.filter((item: any) => {
			delete item.id;
			return item.refProjectId || item.refPeriodId;
		}) || [];
	}

	buildPayload(periods: any[]) {
		const periodId = this.qcPeriodFormGroup.get('periodId')?.value;
		return {
			qcProjectId: this.qcProjectID,
			periodId: periodId,
			status: this.qcPeriodFormGroup.get('status')?.value,
			periods: periods,
			stockInitialization: {
				stockBaseProjectId: parseInt(this.qcPeriodFormGroup.get('stockBaseProjectId')?.value),
				stockPeriodId: parseInt(this.qcPeriodFormGroup.get('stockPeriodId')?.value),
			},
		};
	}

	handleQCPeriodCreateSuccess() {
		this.qcPeriodFormModal = false;
		const periodName = this.periodList.find((p: any) => p.value === this.qcPeriodFormGroup.get('periodId')?.value)?.label || '';
		const title = 'QC Period Added';
		const message = '' + periodName;
		this.notifyWidget(title, 'success', message);
		this.loadQCPeriodTableData = false;
    	this.qcperiodError = false;
	}

	handleQCPeriodCreateError(error: any) {
    	this.qcperiodError = true;
		let title: string;
		let message: string;
		if ([400, 401, 404, 500, 502, 503, 504].includes(error.status)) {
			if (error.status == 400 && error.error.value == 'A QCPeriod with the specified PeriodId already exists for this QCProjectId.') {
				title = 'Error: A similar QC period already exists.';
				this.notifyWidget(title, 'error');
			}
			else {
				title = 'Error: ' + error.status + '. QC Period creation failed. Please contact administrator.';
				this.notifyWidget(title, 'error');
			}
		}
		else if (error.status === 403) {
			UserService.forbiddenError = true;
			title = 'Operation Restricted';
			message = 'Insufficient permission for selected BP Country';
			this.notifyWidget(title, 'error', message);
		}
		else {
			title = error?.message == null ? error?.error : error?.message;
			this.notifyWidget(title, 'error');
		}
		this.loadQCPeriodTableData = false;
    	this.qcPeriodFormModal = false;
	}

	completeQCPeriodCreateRequest() {
		this.getQCPeriodByQCProjectID(this.qcProjectID);
		this.qcPeriodFormModal = false;
	}

	updateQCPeriod() {
		const periods: any = [];
		this.qcPeriodFormGroup.get('periods')?.value.forEach((item: any) => {
			delete item.id
			if (item.refProjectId || item.refPeriodId) {
				periods.push(item);
			}
		});
		const payload = {
			periodId: this.qcPeriodFormGroup.get('periodId')?.value,
			periods: periods,
			stockInitialization: {
				stockBaseProjectId: parseInt(this.qcPeriodFormGroup.get('stockBaseProjectId')?.value),
				stockPeriodId: parseInt(this.qcPeriodFormGroup.get('stockPeriodId')?.value)
			}
		}
		this.putApiService.updateQCPeriod(payload, this.qcPeriodID)
			.subscribe({
				next: () => {
					const selectedQCPeriodNames: string[] = this.getSelectedQCPeriodNames();
					this.qcPeriodFormModal = false;
					const name = selectedQCPeriodNames[0] ? selectedQCPeriodNames[0] : '';
					const title = 'QC Period Updated';
					const message = name;
					this.notifyWidget(title, 'success', message);
					this.loadQCPeriodTableData = false;
				},
				error: (error) => {
					this.qcPeriodFormModal = false;
					let title: string;
					let message: string;
					if (error.status == 400 || error.status == 401 || error.status == 404 || error.status == 500 || error.status == 502 || error.status == 503 || error.status == 504) {
						title = 'Error: ' + error.status + '. QC Period update failed. Please contact administrator.';
						if (error.error.message.includes('QC Status')) {
							this.openQCEditValidationModal();
						}
						else {
							this.notifyWidget(title, 'error');
						}
					}
					else if (error.status == 403) {
						UserService.forbiddenError = true;
						title = 'Operation Restricted';
						message = 'Insufficient permission for selected BP Country';
						this.notifyWidget(title, 'error', message);
					}
					else {
						title = error?.message == null ? error?.error : error?.message;
						this.notifyWidget(title, 'error');
					}
					this.loadQCPeriodTableData = false;
				},
				complete: () => {
					this.getQCPeriodByQCProjectID(this.qcProjectID);
					this.qcPeriodFormModal = false;
				}
			});
	}

	/**
	 * @name  openCreateBulkQCPeriodModal
	 * @description open modal for creating bulk qc period
	*/
	openCreateBulkQCPeriodModal() {
		this.bulkQCPeriodFormGroup = this.formBuilder.group({
			startPeriod: ['', { nonNullable: true }],
			endperiod: this.selectedEntities[0].periodId.toString(),
		});

		// Get the source period ID to filter it out from start period options
		const sourcePeriodId = this.selectedEntities[0].periodId.toString();

		// Use periodList instead of currentPeriodList to include future dates
		// Filter out the source period to prevent creating bulk QC with same start and end period
		const availablePeriods = this.periodList && this.periodList.length > 0
			? this.periodList
			: this.currentPeriodList || [];

		this.periodListForBulkQCPeriod = availablePeriods.filter(period => period.value !== sourcePeriodId);

		this.createBulkQCPeriodModal = true;
		this.bulkQCPeriodFormGroup.get('endperiod')?.disable();
	}

	/**
	 * @name  getBulkQCEndPeriodText
	 * @description get the end period display text for bulk QC creation
	*/
	getBulkQCEndPeriodText(): string {
		const endPeriodId = this.bulkQCPeriodFormGroup?.get('endperiod')?.value;
		if (!endPeriodId) {
			return '';
		}

		// First try to find in currentPeriodList
		if (this.currentPeriodList && this.currentPeriodList.length > 0) {
			const endPeriod = this.currentPeriodList.find((period: any) => period.value === endPeriodId);
			if (endPeriod) {
				return endPeriod.label;
			}
		}

		// If not found in currentPeriodList, try periodList
		if (this.periodList && this.periodList.length > 0) {
			const endPeriod = this.periodList.find((period: any) => period.value === endPeriodId);
			if (endPeriod) {
				return endPeriod.label;
			}
		}

		return '';
	}

	/**
	 * @name  saveBulkQCPeriod
	 * @description allows modal to close if cancel is clicked or directed towards createBulkQCPeriod method
	*/
	saveBulkQCPeriod(confirmsave?: boolean): void {
		if (!confirmsave) {
			this.createBulkQCPeriodModal = false;
		}
		else {
			this.loadQCPeriodTableData = true;
			this.createBulkQCPeriod();
		}
	}

	/**
	 * @name  createBulkQCPeriod
	 * @description creates bulk qc period
	*/
	createBulkQCPeriod() {
		const payload = {
			startPeriod: this.bulkQCPeriodFormGroup.get('startPeriod')?.value,
			endperiod: this.bulkQCPeriodFormGroup.get('endperiod')?.value
		}
		this.postApiService.createBulkQCPeriod(payload, this.qcProjectID)
			.subscribe({
				next: (result: any) => {
					let nonCreated = false;
					let title: string;
					nonCreated = result.bulkQCPeriods.every((response: any) => response.statusCode == 400);
					if (nonCreated) {
						title = 'Error: A similar QC period already exists.';
						this.notifyWidget(title, 'error');
					}
					else {
						title = 'QC Period Added';
						const message = '';
						this.notifyWidget(title, 'success', message);
					}
					this.createBulkQCPeriodModal = false;
					this.loadQCPeriodTableData = false;
				},
				error: (error) => {
					this.createBulkQCPeriodModal = false;
					let title: string;
					let message: string;
					if (error.status == 400 || error.status == 401 || error.status == 404 || error.status == 500 || error.status == 502 || error.status == 503 || error.status == 504) {
						if (error.status == 400 && error.error.value == 'A QCPeriod with the specified PeriodId already exists for this QCProjectId.') {
							title = 'Error: A similar QC period already exists.';
							this.notifyWidget(title, 'error');
						}
						else {
							title = 'Error: ' + error.status + '. QC Period creation failed. Please contact administrator.';
							this.notifyWidget(title, 'error');
						}
					}
					else if (error.status == 403) {
						UserService.forbiddenError = true;
						title = 'Operation Restricted';
						message = 'Insufficient permission for selected BP Country';
						this.notifyWidget(title, 'error', message);
					}
					else {
						title = error?.message == null ? error?.error : error?.message;
						this.notifyWidget(title, 'error');
					}
					this.loadQCPeriodTableData = false;
				},
				complete: () => {
					this.getQCPeriodByQCProjectID(this.qcProjectID);
					this.createBulkQCPeriodModal = false;
				}
			});
	}


	resetAutoGenerateName() {
		this.firsttime = false;
		this.disableAutoGenerateName = true;
		this.addBaseProjectFormGroup.get('baseProjectName')?.setValue('');
	}

	updateCombinedName() {
		const baseProjectName = this.addBaseProjectFormGroup.get('baseProjectName')?.value || '';
		const suffixes = this.addBaseProjectFormGroup.get('suffixes')?.value || '';
		const combinedName = `${baseProjectName} ${suffixes}`.trim();
		this.addBaseProjectFormGroup.get('combinedName')?.setValue(combinedName, { emitEvent: false });
	}
	//QC Period

	//QC Security
	getUsersList() {
		this.users$ = this.getApiService.getURMUsers({
			userName: '',
			firstName: 'a*',
			lastName: '',
			clientId: 148,
			excludeLockedUsers: true,
			filterUsersByRole: false,
			roleFilter: '',
			pageSize: 50
		}).pipe(
			map((users: any) =>
				users.map((d) => ({
					name: d.lastName + ', ' + d.firstName + ' (' + d.userName + ')',
					id: d.userId,
					userName: d.userName,
					fullName: d.firstName + ' ' + d.lastName,
					email: d.eMail
				}))
			)
		);
		this.users$.subscribe({
			next: (result) => {
				this.userList = result;
			}
		});
	}

	userkey(val: any): void {
		if (typeof val != 'object') {
			clearTimeout(this.userSearchInterval);
			this.userSearchInterval = setTimeout(() => {
				const params = {
					userName: val.length > 1 ? val + '*' : '',
					firstName: val ? val + '*' : 'a*',
					lastName: val ? val + '*' : 'a*',
					clientId: 148,
					excludeLockedUsers: true,
					filterUsersByRole: false,
					roleFilter: '',
					pageSize: 500
				};
				this.users$ = this.getApiService.getURMUsers(params).pipe(
					map((users: any) =>
						users.map((d) => ({
							name: d.lastName + ', ' + d.firstName + ' (' + d.userName + ')',
							id: d.userId,
							userName: d.userName,
							fullName: d.firstName + ' ' + d.lastName,
							email: d.eMail
						}))
					)
				);
				this.users$.subscribe({
					next: (result: any) => {
						this.userList = result;
					}
				});
			}, 500);
		}
	}

	openAddQCSecurityUsersModal() {
		this.getUsersList();
		this.addQCSecurityUsersFormInitialization();
	}

	private setupQCSecurityUsersFormValueChanges(): void {
	    this.addQCSecurityUsersForm.valueChanges.subscribe(() => {
		const usersSelected = this.addQCSecurityUsersForm.get('users')?.value?.length > 0;
		const commaInput = this.parseCommaSeparatedUsernames().length > 0;
		this.addQCSecurityUsersForm.get('users')?.setValidators(usersSelected || commaInput ? null : Validators.required);
		this.addQCSecurityUsersForm.get('users')?.updateValueAndValidity({ emitEvent: false });
	});
	}

	addQCSecurityUsersFormInitialization() {
		this.addQCSecurityUsersForm = this.formBuilder.group({
			users: [[]],
			commaSeparatedUsernames: [''] 
			}, { validators: this.atLeastOneUserValidator 
		});
  		this.setupQCSecurityUsersFormValueChanges();
		this.addQCSecurityUsersModal = true;
	}

	closeAddQCSecurityUsersModal(event: boolean) {
		this.showQCSecurityTable = true;
		this.addQCSecurityUsers(event);
	}

	addQCSecurityUsers(confirmsave?: boolean) {
		if (!confirmsave) {
			this.addQCSecurityUsersModal = false;
			this.addQCSecurityUsersForm.reset();
		}
		else {
			setTimeout(() => {
				this.loadQCSecurityTableData = true;
			});

			this.getAssignedQCUserIds().subscribe({
				next: (result) => {
					// Store invalid usernames for modal display
					this.invalidUsernames = result.invalidUsernames;

					if (result.invalidUsernames.length > 0 && result.userIds.length === this.addQCSecurityUsersForm.value.users.length) {
						// Only invalid usernames were entered, show modal and don't proceed
						this.showInvalidUsernamesModal = true;
						this.loadQCSecurityTableData = false;
						// Still need to reset the modal and form
						this.addQCSecurityUsersModal = false;
						this.addQCSecurityUsersForm.reset();
						return;
					}

					const payload = this.qcSecurityUsersPayload(result.userIds);
					this.postApiService.addUsersForQCSecurity(payload).subscribe({
						next: (response) => this.handleQCSecurityUsersResponse(response),
						error: (error) => this.handleQCSecurityUsersError(error),
						complete: () => this.completeQCSecurityUsersRequest(),
					});
				},
				error: (error) => {
					console.error('Error getting assigned user IDs:', error);
					this.loadQCSecurityTableData = false;
					// Reset the modal and form on error
					this.addQCSecurityUsersModal = false;
					this.addQCSecurityUsersForm.reset();
				}
			});
		}
	}


  getAssignedQCUserIds(): Observable<{userIds: number[], invalidUsernames: string[]}> {
	const selectedUsers = this.addQCSecurityUsersForm.value.users.map((user: any) => user?.id);
	const commaUsernames = this.parseCommaSeparatedUsernames();

	if (commaUsernames.length === 0) {
	  return of({userIds: selectedUsers, invalidUsernames: []});
	}

	// Save the original usernames for comparison
	const originalUsernames = [...commaUsernames];

	return this.getApiService.getURMUsersfromUsernames(commaUsernames).pipe(
	  map((urmUsers: any[]) => {
		const urmUserIds = urmUsers.map(user => user.userId);
		const responseUsernames = urmUsers.map(user => user.userName);

		// Find usernames that didn't get a response (invalid)
		const invalidUsernames = originalUsernames.filter(username =>
		  !responseUsernames.some(responseUser => responseUser.toLowerCase() === username.toLowerCase())
		);

		return {
		  userIds: [...new Set([...selectedUsers, ...urmUserIds])],
		  invalidUsernames
		};
	  }),
	  catchError((error) => {
		console.error('Error fetching users from URM:', error);
		const matchedUsers = this.userList
		  .filter(user => commaUsernames.some(u => u.toLowerCase() === user.userName.toLowerCase()));
		const matchedCommaUserIds = matchedUsers.map(user => user.id);
		const responseUsernames = matchedUsers.map(user => user.userName);

		// Find usernames that didn't get a response (invalid)
		const invalidUsernames = originalUsernames.filter(username =>
		  !responseUsernames.some(responseUser => responseUser.toLowerCase() === username.toLowerCase())
		);

		return of({
		  userIds: [...new Set([...selectedUsers, ...matchedCommaUserIds])],
		  invalidUsernames
		});
	  })
	);
  }


onCommaInputChange(event: Event): void {
  const input = (event.target as HTMLInputElement).value;
  this.addQCSecurityUsersForm.get('commaSeparatedUsernames')?.setValue(input);
}

atLeastOneUserValidator = (group: FormGroup): ValidationErrors | null => {
  const users = group.get('users')?.value || [];
  const commaSeparated = group.get('commaSeparatedUsernames')?.value || '';

  const hasChipUsers = Array.isArray(users) && users.length > 0;
  const hasCommaUsers = commaSeparated
    .split(',')
    .map((u: string) => u.trim())
    .filter(Boolean).length > 0;

  return hasChipUsers || hasCommaUsers ? null : { noUsers: true };
};

getAssignedUserIds() {
  const selectedUsers = this.addQCSecurityUsersForm.value.users.map((user: any) => user?.id);
  const commaUsernames = this.parseCommaSeparatedUsernames();
  const matchedUsers = this.userList.filter(user => commaUsernames.includes(user.userName));
  const matchedCommaUserIds = matchedUsers.map(user => user.id);

  // Show warning for unmatched usernames
  const unmatched = commaUsernames.filter(u => !matchedUsers.some(user => user.userName === u));
  if (unmatched.length) {
    this.notificationService.showNotification(
      'Some usernames were not found: ' + unmatched.join(', '),
      { variant: NotificationVariant.WARNING }
    );
  }

  return [...new Set([...selectedUsers, ...matchedCommaUserIds])];
}

parseCommaSeparatedUsernames(): string[] {
  const raw = this.addQCSecurityUsersForm.get('commaSeparatedUsernames')?.value || '';
  return raw
	.split(',')
	.map((u: string) => u.trim())
	.filter(Boolean);
}

hasAnyUserInput(): boolean {
  const usersSelected = this.addQCSecurityUsersForm?.get('users')?.value?.length > 0;
  const commaInput = this.parseCommaSeparatedUsernames().length > 0;
  return usersSelected || commaInput;
}


	qcSecurityUsersPayload(assignedUsers: any[]) {
		return {
      		projectTypeId: 2,
			userIds: assignedUsers,
			projectIds: [this.qcProjectID],
		};
	}

	handleQCSecurityUsersResponse(response: any) {
		// Show invalid usernames modal if any exist
		if (this.invalidUsernames.length > 0) {
			this.showInvalidUsernamesModal = true;
		}

		if (response.projectResponses.length > 1) {
			this.checkMultipleResponses(response);
		}
		else {
			this.checkSingleResponse(response);
		}
	}



	checkMultipleResponses(response: any) {
		let title = '';
		if (response.projectResponses.every((res: any) => res.statusMsg.includes('User(s) already assigned'))) {
			title = 'Error: User already exists.';
			this.notifyWidget(title, 'error');
		}
		else if (response.projectResponses.find((res: any) => res.statusMsg.includes('duplicate/master users skipped') || res.statusMsg.includes('already assigned'))) {
			title = 'Note: Duplicate / Master users skipped';
			this.notifyWidget(title, 'warn');
		}
		else {
			title = 'Users Assigned Successfully.';
			this.notifyWidget(title, 'success');
		}
		this.loadQCSecurityTableData = false;
	}

	checkSingleResponse(response: any) {
		let title = '';
		if (response.projectResponses.find((res: any) => res.statusMsg.includes('duplicate/master users skipped'))) {
			title = 'Note: Duplicate / Master users skipped';
			this.notifyWidget(title, 'warn');
		}
		else if (response.projectResponses.find((res: any) => res.statusMsg.includes("Either you don't have permission"))) {
			title = "Master Role user is auto-assigned and can't be added manually.";
			this.notifyWidget(title, 'warn');
		}
		else if (response.projectResponses.find((res: any) => res.statusMsg.includes('All users assigned successfully'))) {
			title = 'Users Assigned Successfully.';
			this.notifyWidget(title, 'success');
		}
		else {
			title = 'Error: User already exists.';
			this.notifyWidget(title, 'error');
		}
		this.loadQCSecurityTableData = false;
	}

	handleQCSecurityUsersError(error: any) {
		let title: string;
		let message = '';
		if ([400, 401, 404, 500, 502, 503, 504].includes(error.status)) {
			title = 'Error: ' + error.status + '. Users assigning failed. Please contact administrator.';
			this.notifyWidget(title, 'error');
		}
		else if (error.status == 403) {
			UserService.forbiddenError = true;
			title = 'Operation Restricted';
			message = 'Insufficient permission for selected BP Country';
			this.notifyWidget(title, 'error', message);
		}
		else {
			title = error?.message == null ? error?.error : error?.message;
			this.notifyWidget(title, 'error');
		}
		this.addQCSecurityUsersModal = false;
		this.loadQCSecurityTableData = false;
		this.showQCSecurityTable = false;
	}

	completeQCSecurityUsersRequest() {
		this.getQCProjectAssignedUsers();
		this.getUsersList();
		this.addQCSecurityUsersModal = false;
		this.addQCSecurityUsersForm.reset();
		// Reset validation arrays
		this.invalidUsernames = [];
	}

	closeInvalidUsernamesModal() {
		this.showInvalidUsernamesModal = false;
		this.invalidUsernames = [];
		// Reset the form to clear invalid data
		this.addQCSecurityUsersForm.reset();
		this.addQCSecurityUsersModal = false;
	}

	getQCProjectAssignedUsers() {
		this.loadQCSecurityTableData = true;
		this.getApiService.getQCProjectAssignedUsers(this.qcProjectID, 2).subscribe({
			next: (result: any) => {
				const userIds: number[] = [];
				result.users.forEach((user: any) => {
					userIds.push(user.id);
					if(user.assignedOn){
						const date = new Date(user.assignedOn.toString());
						const timeZoneAbbreviation = this.getOffsetAtTime(this.timezone, user.assignedOn);
						const cetDate = moment(date).tz(this.timezone).format('YYYY-MM-DD HH:mm') + " " + timeZoneAbbreviation;
						user.lastAssignedOn = cetDate;
					}
					else{
						user.lastAssignedOn = 'N/A';
					}
				});
				this.getURMUsersByUserId(userIds, result.users);
			},
			error: (error) => {
				let title: string;
				if (error.status == 404) {
					this.assignedUsersData = null;
					this.visibleAssignedUsersList = [];
					this.showQCSecurityTable = false;
				}
				else {
					if (
						error.status == 400 ||
						error.status == 401 ||
						error.status == 500 ||
						error.status == 502 ||
						error.status == 503 ||
						error.status == 504
					) {
						title = 'Error: ' + error.status + '. Unable to fetch Assigned Users List. Please contact administrator.';
						this.notifyWidget(title, 'error');
					}
				}
				this.loadQCSecurityTableData = false;
			}
		});
	}

	getURMUsersByUserId(data: number[], userList: any) {
		this.getApiService.getURMUsersByUserId(data)
			.subscribe({
				next: (result: any) => {
					result.forEach((item: any) => {
						userList.forEach((user: any) => {
							if (item.userId === user.id) {
								user.userName = item.userName;
								user.fullName = item.firstName + ' ' + item.lastName;
								user.email = item.eMail;
							}
						})
					})
					this.assignedUsersData = userList;
					this.unfilteredAssignedUsersData = [...this.assignedUsersData];
					this.visibleAssignedUsersList = this.getPageAssignedUsers(this.currentPageForAssignedUsers, this.pageSizeForAssignedUsers);
					GetApiService.loadQCSecurityTableData = false;
					this.loadQCSecurityTableData = false;
				},
				error: (error) => {
					let title: string;
					if (error.status == 404) {
						this.assignedUsersData = null;
						this.visibleAssignedUsersList = [];
						title = 'No records found.';
						this.notifyWidget(title, 'error');
					}
					else {
						if (
							error.status == 400 ||
							error.status == 401 ||
							error.status == 500 ||
							error.status == 502 ||
							error.status == 503 ||
							error.status == 504
						) {
							title = 'Error: ' + error.status + '. Unable to fetch Assigned Users List. Please contact administrator.';
							this.notifyWidget(title, 'error');
						}
					}
					this.loadQCSecurityTableData = false;
				}
			})
	}

	onPageChangeAssignedUsers(event: PageChange) {
		this.currentPageForAssignedUsers = event.pageIndex;
		this.pageSizeForAssignedUsers = event.pageSize;
		this.visibleAssignedUsersList = this.getPageAssignedUsers(this.currentPageForAssignedUsers, this.pageSizeForAssignedUsers);
    	this.cdRef.markForCheck();
	}

	private calculatePageStartForAssignedUsers(page: number, size: number): number {
		return (page - 1) * size;
	}

	private calculatePageEndForAssignedUsers(page: number, size: number): number {
		return page === 0 ? size : page * size;
	}

	private getPageAssignedUsers(page: number, size: number) {
		const start = this.calculatePageStartForAssignedUsers(page, size);
		const end = this.calculatePageEndForAssignedUsers(page, size);
		return this.assignedUsersData.slice(start, end);
	}

	openRemoveAssignedUsersModal() {
		this.removeAssignedUsersConfirmationModal = true;
	}

	removeAssignedUsers(confirmDeletion?: boolean): void {
		if (!confirmDeletion) {
			this.removeAssignedUsersConfirmationModal = false;
		}
		else {
			this.loadQCSecurityTableData = true;
			this.removeAssignedUsersConfirmationModal = false;
			const selectedAssignedUserIds: number[] = this.selectedEntities.map((item: any) => item.id);
			this.deleteApiService.removeAssignedUsers({projectTypeId:2 ,userIds: selectedAssignedUserIds, projectId: this.qcProjectID }).subscribe({
				next: (result: any) => {
					this.handleDeleteSuccessResponse(result.projectResponses[0], selectedAssignedUserIds);
				},
				error: (error) => {
						let title: string;
						let message = '';
						if (error.status == 400 || error.status == 401 || error.status == 404 || error.status == 500 || error.status == 502 || error.status == 503 || error.status == 504) {
							title = 'Error: ' + error.status + '. Unable to remove assigned users. Please contact administrator.';
							this.notifyWidget(title, 'error');
						}
						else if (error.status == 403) {
							UserService.forbiddenError = true;
							title = 'Operation Restricted';
							message = 'Insufficient permission for selected BP Country';
							this.notifyWidget(title, 'error', message);
						}
						this.loadQCSecurityTableData = false;
				},
			});
		}
	}

	handleDeleteSuccessResponse(result: any, selectedAssignedBpUserIds: Array<number>): void {
		if (result.statusCode === 403 || result.statusMsg.includes('Following User IDs belong to master users and skipped from removal')) {
			this.handlePartialDelete(result);
		} 
    else {
			this.handleSuccessfulDeletion(selectedAssignedBpUserIds);
		}
	}

  	handleSuccessfulDeletion(selectedAssignedBpUserIds: Array<number>): void {
		const title = 'Assigned Users Removed!';
		this.notifyWidget(title, 'success');
		setTimeout(() => {
			this.clearSelection();
			this.getQCProjectAssignedUsers();
		}, 500);
	}

	handlePartialDelete(result: any): void {
		if (result.statusCode === 403) {
			const title = 'Operation Restricted';
			const message = 'Master level user cannot be removed manually.';
			this.notifyWidget(title, 'error', message);
			this.loadQCSecurityTableData = false;
		} 
    else {
			this.processDeleteResults(result);
      		this.openDeleteUserValidationModal();
		}
	}

	processDeleteResults(result: any): void {
		const userIds = result.statusMsg.match(/\d+/g);
		const deleteConflictUser = userIds
		.map(id => parseInt(id, 10)) 
		.map(id => this.selectedEntities.find(user => user.id === id))
		.filter(user => user !== undefined);
			this.deleteConflictUser = deleteConflictUser;
	}

	openDeleteUserValidationModal() {
		this.deleteUserValidationModal = true;
	}

	closeDeleteUserValidationModal() {
		setTimeout(() => {
			this.clearSelection();
			this.getQCProjectAssignedUsers();
		}, 500);
		this.deleteUserValidationModal = false;
	}

	closeClick() {
		this.inputValue = '';
		this.currentPageForAssignedUsers = 1;
		this.assignedUsersData = [...this.unfilteredAssignedUsersData];
		this.visibleAssignedUsersList = this.getPageAssignedUsers(this.currentPageForAssignedUsers, this.pageSizeForAssignedUsers);
	}

  	onInputChange(value: string): void {
		if (value) {
		this.inputValue = value;
		const lowerCaseQuery = value.toLowerCase();      
		this.assignedUsersData = this.unfilteredAssignedUsersData.filter(user => {
			return (
				(user.userName && user.userName.toLowerCase().includes(lowerCaseQuery)) ||
				(user.fullName && user.fullName.toLowerCase().includes(lowerCaseQuery)) ||
				(user.email && user.email.toLowerCase().includes(lowerCaseQuery)) ||
				(user.lastAssignedOn && user.lastAssignedOn.toLowerCase().includes(lowerCaseQuery))
			);
		});
		} 
		else {
			this.inputValue = '';
			this.assignedUsersData = [...this.unfilteredAssignedUsersData];
		}
		this.currentPageForAssignedUsers = 1;
		this.visibleAssignedUsersList = this.getPageAssignedUsers(this.currentPageForAssignedUsers, this.pageSizeForAssignedUsers);
	}
	//QC Security

	getOffsetAtTime(timezone, date) {
		const zone = moment.tz.zone(timezone);

		if (zone) {
			const timestamp = moment(date).valueOf(); // Convert date to timestamp
			const abbrs = zone.abbrs; // Get the list of abbreviations
			const untils = zone.untils; // Get the list of timestamp changes (for daylight saving time changes)

			// Find the correct abbreviation based on the timestamp
			for (let i = 0; i < untils.length; i++) {
			if (timestamp < untils[i]) {
				return abbrs[i]; // Return abbreviation if timestamp is before the DST change
			}
			}

			// If no matching change is found (for times after the last DST change), use the last abbreviation
			return abbrs[abbrs.length - 1]; // Return the last abbreviation
		} else {
			return null; // Return null if the timezone is not found
		}
	}

	//bp security
	getProjectAssignedBpUsers(resetTable?: boolean) {
		this.loadTableData = true;
		this.getApiService.getQCProjectAssignedUsers(this.baseProjectID, 1).subscribe({
			next: (result: any) => {
				const userIds: number[] = [];
				result.users.forEach((user: any) => {
					userIds.push(user.id);
					if(user.assignedOn){
						const date = new Date(user.assignedOn.toString());
						const timeZoneAbbreviation = this.getOffsetAtTime(this.timezone, user.assignedOn);
						const cetDate = moment(date).tz(this.timezone).format('YYYY-MM-DD HH:mm') + " " + timeZoneAbbreviation;
						user.lastAssignedOn = cetDate;
					}
					else{
						user.lastAssignedOn = 'N/A';
					}
				});
				this.getURMUsersByUserIdForBp(userIds, result.users);
				this.loadTableData = false;
			},
			error: (error) => {
				let title: string;
				if (error.status == 404) {
					this.getApiService.setBPSecurityUserListSubject([]);
				} 
				else {
					if ([400, 401, 500, 502, 503, 504].includes(error.status)) {
						title = 'Error: ' + error.status + '. Unable to fetch Assigned Users List. Please contact administrator.';
						this.getApiService.setBPSecurityUserListSubject([]);
						this.notifyWidget(title, 'error');
					}
				}
				this.loadTableData = false;
			}
		});
	}
	
	getURMUsersByUserIdForBp(data: number[], bpUserList: any) {
		this.getApiService.getURMUsersByUserId(data)
		.subscribe({
			next: (result: any) => {
				result.forEach((item: any) => {
					bpUserList.forEach((user: any) => {
					if (item.userId === user.id) {
						user.userName = item.userName;
						user.fullName = item.firstName + ' ' + item.lastName;
						user.email = item.eMail;
					}
					});
				});
				this.getApiService.setBPSecurityUserListSubject(bpUserList);
				this.loadTableData = false;
				this.cdRef.detectChanges();
			},
			error: (error) => {
				let title: string;
				if (error.status == 404) {
					this.getApiService.setBPSecurityUserListSubject([]);
					title = 'No records found.';
					this.notifyWidget(title, 'error');
				} else {
					if ([400, 401, 500, 502, 503, 504].includes(error.status)) {
						title = 'Error: ' + error.status + '. Unable to fetch Assigned Users List. Please contact administrator.';
						this.getApiService.setBPSecurityUserListSubject([]);
						this.notifyWidget(title, 'error');
					}
				}
				this.loadTableData = false;
			}
		});
	}
	//bp security

	notifyWidget(title: string, notificationType: string, message?: string): void {
		if (notificationType == 'info') {
			this.notificationService.showNotification(title, {
				variant: NotificationVariant.INFO,
				message: message || ''
			});
		}
		else if (notificationType == 'warn') {
			this.notificationService.showNotification(title, {
				variant: NotificationVariant.WARNING,
				message: message || ''
			});
		}
		else if (notificationType == 'error') {
			this.notificationService.showNotification(title, {
				message: message || '',
				duration: 15000,
				variant: NotificationVariant.ERROR,
			});
		}
		else {
			this.notificationService.showNotification(title, {
				message: message || '',
				duration: 15000,
				variant: NotificationVariant.SUCCESS,
			});
		}
	}

	openQCDeleteValidationModal() {
		this.QCDeleteValidationModal = true;
	}

	closeQCDeleteValidationModal(): void {
		this.QCDeleteValidationModal = false;
		this.QCPeriodQCStatusIds = [];
	}

	openQCEditValidationModal() {
		this.QCEditValidationModal = true;
	}

	closeQCEditValidationModal(): void {
		this.QCEditValidationModal = false;
	}

	openpgEditValidationModal() {
		this.pgEditValidationModal = true;
	}

	closepgEditValidationModal(): void {
		this.pgEditValidationModal = false;
	}

	openpgEditQCPeriodValidationModal() {
		this.pgEditQCPeriodValidationModal = true;
	}

	closepgEditQCPeriodValidationModal(confirmsave?: boolean): void {
		if(confirmsave){
			this.updateBaseProject();
			this.pgEditQCPeriodValidationModal = false;
		}
		else{
			this.pgEditQCPeriodValidationModal = false;
			this.loadTableData = false;
		}
	}
	//bp associations
	getProjectAssociations() {
		this.loadTableData = true;
		this.getApiService.getBPAssociations(this.baseProjectID).subscribe({
			next: (result: any) => {
				const convertDates = (projects: any[]) => {
					projects.forEach((project: any) => {
						if (project.changedWhen) {
							const date = project.changedWhen.toString();
							const timeZoneAbbreviation = this.getOffsetAtTime(this.timezone, project.changedWhen);
							const cetDate = moment.utc(date).tz(this.timezone).format('YYYY-MM-DD HH:mm') + " " + timeZoneAbbreviation;
							project.changedWhen = cetDate;
						}
					});
				};
				convertDates(result.rbbpProjects || []);
				convertDates(result.productionProjects || []);
				convertDates(result.reportingProjects || []);
				this.rbbpProjects = result.rbbpProjects || [];
				this.productionProjects = result.productionProjects || [];
				this.reportingProjects = result.reportingProjects || [];
				this.currentProjects = result.productionProjects;
				if(this.rbbpProjects.length==0 && this.productionProjects.length==0
					&& this.reportingProjects.length==0
				)
				this.showAssociationsTable=false;
				this.visibleCurrentProjects = this.getPageAssoications(this.currentPage, this.pageSize);
			},
			error: (error) => {
				let title: string;
				if (error.status == 404) {
					this.getApiService.setBPSecurityUserListSubject([]);
				} 
				else {
					if ([400, 401, 500, 502, 503, 504].includes(error.status)) {
						title = 'Error: ' + error.status + '. Unable to fetch Assigned Users List. Please contact administrator.';
						this.getApiService.setBPSecurityUserListSubject([]);
						this.notifyWidget(title, 'error');
					}
				}
				this.loadTableData = false;
			}
		});
	}
	updateCurrentProject(event){
		this.currentPageForAssociatedProjects =1;
		this.currentProjectType=event;
		switch (event) {
			case 'ProductionProject':
				this.currentProjects= this.productionProjects;
				break;
			case 'ReportingProject':
				this.currentProjects= this.reportingProjects;
				break;
			case 'RBBaseProject':
				this.currentProjects= this.rbbpProjects;
			break;
		}
		this.visibleCurrentProjects = this.getPageAssoications(this.currentPageForAssociatedProjects, this.pageSizeForAssignedUsers);
    	

	}
	private getPageAssoications(page: number, size: number) {
		const start = this.calculatePageStart(page, size);
		const end = this.calculatePageEnd(page, size);
		return this.currentProjects.slice(start, end);
	}

	onPageChangeAssociations(event: PageChange) {
		this.currentPageForAssociatedProjects = event.pageIndex;
		this.pageSizeForAssignedUsers = event.pageSize;
		this.visibleCurrentProjects = this.getPageAssoications(this.currentPageForAssociatedProjects, this.pageSizeForAssignedUsers);
    	this.cdRef.markForCheck();
	}

}
