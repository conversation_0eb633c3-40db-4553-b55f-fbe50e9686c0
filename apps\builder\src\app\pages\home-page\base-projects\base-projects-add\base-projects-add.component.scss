@use 'sass:map' as map;
@use '../../../../../styles/theme/gfk-light.palette.scss' as gfk;
@use '@gfk/style' as gfk-style;

.heading {
	border-top: 1px solid rgb(223 226 229);
    border-bottom: 1px solid rgb(223 226 229);
    padding: 10px 0px
}

.gfk-text-input {
    width: 65% !important;
}

.icon-color {
    line-height: 0.5;
    background-color: gfk-style.$brand;
    color: gfk-style.$white;
    border-radius: 50%;
    position: absolute;
    left: 165px;
    margin-top: 10px;
}

.last-update-time-text {
    color: #b8b8b8;
    font-size: 12px;
    font-weight: 700;
}

.transparent-background-delete-btn {
    background-color: #ffffff !important;
    color: #bc1c0e !important;
    padding: 6px 10px !important;
    border: 2px solid #bc1c0e !important;
    border-radius: 5px !important;
    font-weight: 700 !important;
    .stroke-brand {
        stroke: #bc1c0e;
    }
    .fill-brand {
        fill: #bc1c0e;
    }
}

.transparent-background-IRNotRequested-btn {
  background-color: #ffffff !important;
  color: #e55a00 !important;
  padding: 6px 10px !important;
  border: 2px solid #e55a00 !important;
  border-radius: 5px !important;
  font-weight: 700 !important;
  .stroke-brand {
      stroke: #e55a00;
  }
  .fill-brand {
      fill: #e55a00;
  }
}

.transparent-background-IR-btn {
  background-color: #ffffff !important;
  color: #a39c9c !important;
  padding: 6px 10px !important;
  border: 2px solid #a39c9c !important;
  border-radius: 5px !important;
  font-weight: 700 !important;
  .stroke-brand {
      stroke: #6a6565;
  }
  .fill-brand {
      fill: #6a6565;
  }
}

.transparent-background-Restore-btn {
  background-color: #ffffff !important;
  color: #379930 !important;
  padding: 6px 10px !important;
  border: 2px solid #379930 !important;
  border-radius: 5px !important;
  font-weight: 700 !important;
  .stroke-brand {
      stroke: #379930;
  }
  .fill-brand {
      fill: #379930;
  }
}

.navigation-tabs {
    border-top: 1px solid rgb(223, 226, 229);
    border-bottom: 1px solid rgb(223, 226, 229);
    padding: 15px 25px ;

    .active {
        color: #e55a00;
    }

    .tabs-item:hover {
        cursor: pointer;
        color: #e55a00;
    }
}


.comma-separated-input-group {
  width: 1080px; 
  max-width: 1400px;


  }
.label {
    display: block;
    margin-bottom: 8px;
  }

.CommaSeparatedinput {
    resize: vertical;
    min-height: 52px;
    max-height: 20px;
    padding: 8px 12px; // Add spacing inside the input
    box-sizing: border-box;
    border-color: #eae0e0 ;
    
    &:hover {
  border: 2px solid #565353 !important;
  }
}



.w-fit-content {
    width: fit-content;
}

.selected-qc-project-footer {
	display: flex;
	align-items: center;
	justify-content: space-between;
	background-color: #ebecf0;
	padding: 20px 20px;
	position: fixed;
	width: 100%;
	bottom: 0px;
	z-index: 2;
	.m-0 {
		margin: 0px !important;
	}
	.ml-3 {
		margin-left: 12px !important;
	}
	.stroke-brand {
		stroke: #bc1c0e;
	}
	.fill-brand {
		fill: #bc1c0e;
	}
}

.qc-period-form-modal-height {
  max-height: 80vh;
  overflow-y: auto;
}

.qc-time-position {
    position: absolute;
    bottom: 2%;
}

.bp-conflict-detail-card {
	border: 1px solid #bbc0c9;
    border-radius: 5px;
    padding: 5px 10px;
	margin-bottom: 10px;
}

.bp-conflict-detail-height {
	max-height: 65vh;
    overflow-y: auto;
}

.bp-id-style:hover {
	color: gfk-style.$brand;
}

/* Add positioning for the flyout container */
.filter-list {
    justify-content: space-between;
}

.filter-list .toggle-button > div {
	position: relative; /* Set position to relative */
}

.filter-list input[type='text'] {
	width: 420px;
}

.filter-list .input-container gfk-icon {
	position: absolute;
	top: 5px;
	right: 5px;
}

.filter-list .input-container button{
	z-index: 2;
}

.color-gfk-organge {
	color: gfk-style.$brand;
	cursor: pointer;
}

.inline-search-button {
	position: absolute;
	margin-left: 5px;
}

.filter-list input {
	width: 420px;
	height: 36px;
	padding: 0 12px;
}

button.transparent-background-IR-btn,
button.transparent-background-IRNotRequested-btn,
button.transparent-background-delete-btn,
button.gfk-btn {
    margin-left: 12px; // Adjust spacing between buttons
}

button:last-child {
    margin-left: 12px; // Remove margin from last button to avoid extra gap
}
